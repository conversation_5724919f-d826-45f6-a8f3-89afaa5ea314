<?php
/**
 * Test Filter Validation System
 * Tests the bidirectional cascading filter functionality
 */

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../filter_validation.php';
require_once __DIR__ . '/../DatabaseInteraction.php';

// Test configuration
$test_user_id = 1; // Adjust based on your test user

echo "<h1>Filter Validation System Test</h1>\n";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>\n";

try {
    // Initialize filter validation
    $validator = new FilterValidation();
    $db = new DatabaseInteraction();
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 1: Basic Filter Validation</h2>";
    
    // Test empty filters (should return true if user has any data)
    $empty_filters = [];
    $has_data_empty = $validator->validateFilterCombination($test_user_id, $empty_filters);
    echo "<p><strong>Empty filters validation:</strong> " . ($has_data_empty ? "✅ Has data" : "❌ No data") . "</p>";
    
    // Test with specific data_id
    $specific_filters = ['data_id' => '682b54974bab6'];
    $has_data_specific = $validator->validateFilterCombination($test_user_id, $specific_filters);
    echo "<p><strong>Specific data_id validation:</strong> " . ($has_data_specific ? "✅ Has data" : "❌ No data") . "</p>";
    
    // Test with impossible combination
    $impossible_filters = ['data_id' => 'nonexistent_id_12345'];
    $has_data_impossible = $validator->validateFilterCombination($test_user_id, $impossible_filters);
    echo "<p><strong>Impossible combination validation:</strong> " . ($has_data_impossible ? "❌ Should not have data" : "✅ Correctly no data") . "</p>";
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 2: Filter Options Retrieval</h2>";
    
    // Test getting domain categories
    $domain_options = $validator->getAvailableFilterOptions($test_user_id, 'domain_category', []);
    echo "<p><strong>Available domain categories:</strong> " . count($domain_options) . " options</p>";
    echo "<pre>" . print_r(array_slice($domain_options, 0, 5), true) . "</pre>";
    
    // Test getting data IDs
    $data_id_options = $validator->getAvailableFilterOptions($test_user_id, 'data_id', []);
    echo "<p><strong>Available data IDs:</strong> " . count($data_id_options) . " options</p>";
    echo "<pre>" . print_r(array_slice($data_id_options, 0, 5), true) . "</pre>";
    
    // Test getting sentiments
    $sentiment_options = $validator->getAvailableFilterOptions($test_user_id, 'sentiment', []);
    echo "<p><strong>Available sentiments:</strong> " . count($sentiment_options) . " options</p>";
    echo "<pre>" . print_r($sentiment_options, true) . "</pre>";
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 3: Cascading Filter Dependencies</h2>";
    
    // Test how options change when filters are applied
    $base_filters = [];
    $filtered_filters = ['sentiment' => 'Positive'];
    
    $base_domains = $validator->getAvailableFilterOptions($test_user_id, 'domain_category', $base_filters);
    $filtered_domains = $validator->getAvailableFilterOptions($test_user_id, 'domain_category', $filtered_filters);
    
    echo "<p><strong>Domain categories without filters:</strong> " . count($base_domains) . " options</p>";
    echo "<p><strong>Domain categories with Positive sentiment:</strong> " . count($filtered_domains) . " options</p>";
    
    if (count($filtered_domains) <= count($base_domains)) {
        echo "<p>✅ Filtering correctly reduces options</p>";
    } else {
        echo "<p>❌ Filtering should not increase options</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 4: Batch Filter Options</h2>";
    
    $filter_types = ['domain_category', 'data_id', 'sentiment'];
    $current_filters = ['sentiment' => 'Positive'];
    
    $batch_options = $validator->getBatchFilterOptions($test_user_id, $filter_types, $current_filters);
    
    echo "<p><strong>Batch options request results:</strong></p>";
    foreach ($batch_options as $filter_type => $options) {
        echo "<p>• {$filter_type}: " . count($options) . " options</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 5: Database Method Tests</h2>";
    
    // Test database methods
    $db_validation = $db->validateFilterCombination($test_user_id, ['sentiment' => 'Positive']);
    echo "<p><strong>Database validation (Positive sentiment):</strong> " . ($db_validation ? "✅ Has data" : "❌ No data") . "</p>";
    
    // Test distinct methods
    $partners = $db->getDistinctPartners($test_user_id);
    echo "<p><strong>Distinct partners (product types):</strong> " . count($partners) . " found</p>";

    $lobs = $db->getDistinctLobs($test_user_id);
    echo "<p><strong>Distinct LOBs (channel types):</strong> " . count($lobs) . " found</p>";

    $teams = $db->getDistinctTeams($test_user_id);
    echo "<p><strong>Distinct teams (dummy_1):</strong> " . count($teams) . " found</p>";

    $statuses = $db->getDistinctResolutionStatuses($test_user_id);
    echo "<p><strong>Distinct resolution statuses (dummy_5):</strong> " . count($statuses) . " found</p>";
    
    $date_range = $db->getDateRange($test_user_id);
    echo "<p><strong>Date range:</strong> " . ($date_range['min_date'] ?? 'N/A') . " to " . ($date_range['max_date'] ?? 'N/A') . "</p>";
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 6: Performance Test</h2>";
    
    $start_time = microtime(true);
    
    // Perform multiple operations
    for ($i = 0; $i < 10; $i++) {
        $validator->validateFilterCombination($test_user_id, ['sentiment' => 'Positive']);
        $validator->getAvailableFilterOptions($test_user_id, 'domain_category', ['sentiment' => 'Positive']);
    }
    
    $end_time = microtime(true);
    $execution_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
    
    echo "<p><strong>Performance test (20 operations):</strong> " . round($execution_time, 2) . " ms</p>";
    
    if ($execution_time < 1000) {
        echo "<p>✅ Performance is acceptable (< 1 second)</p>";
    } else {
        echo "<p>⚠️ Performance may need optimization (> 1 second)</p>";
    }
    
    echo "</div>";
    
    echo "<div class='test-section info'>";
    echo "<h2>Test 7: Cache Test</h2>";
    
    // Test cache functionality
    $start_time = microtime(true);
    $result1 = $validator->getAvailableFilterOptions($test_user_id, 'sentiment', []);
    $time1 = microtime(true) - $start_time;
    
    $start_time = microtime(true);
    $result2 = $validator->getAvailableFilterOptions($test_user_id, 'sentiment', []);
    $time2 = microtime(true) - $start_time;
    
    echo "<p><strong>First call:</strong> " . round($time1 * 1000, 2) . " ms</p>";
    echo "<p><strong>Second call (cached):</strong> " . round($time2 * 1000, 2) . " ms</p>";
    
    if ($time2 < $time1) {
        echo "<p>✅ Cache is working (second call faster)</p>";
    } else {
        echo "<p>⚠️ Cache may not be working effectively</p>";
    }
    
    // Clear cache and test
    $validator->clearCache();
    echo "<p>Cache cleared successfully</p>";
    
    echo "</div>";
    
    echo "<div class='test-section success'>";
    echo "<h2>✅ All Tests Completed</h2>";
    echo "<p>Filter validation system is ready for use!</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test the JavaScript filter coordinator on the dashboard</li>";
    echo "<li>Verify AJAX endpoints are working correctly</li>";
    echo "<li>Check filter interactions in the browser</li>";
    echo "<li>Monitor performance in production</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-section error'>";
    echo "<h2>❌ Test Failed</h2>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    echo "</div>";
}

echo "<div class='test-section info'>";
echo "<h2>API Endpoint Tests</h2>";
echo "<p>You can test the API endpoints directly:</p>";
echo "<ul>";
echo "<li><a href='filter_options.php?action=get_options&filter_type=sentiment' target='_blank'>Test sentiment options</a></li>";
echo "<li><a href='filter_options.php?action=validate_combination&sentiment=Positive' target='_blank'>Test validation with Positive sentiment</a></li>";
echo "<li><a href='filter_options.php?action=batch_options&filter_types=domain_category,data_id,sentiment' target='_blank'>Test batch options</a></li>";
echo "<li><a href='data.php?type=validate-filter-combination&sentiment=Positive' target='_blank'>Test data.php validation endpoint</a></li>";
echo "<li><a href='data.php?type=dynamic-filter-options&filter_type=sentiment' target='_blank'>Test data.php dynamic options endpoint</a></li>";
echo "</ul>";
echo "</div>";
?>
