<?php
/**
 * Filter Monitoring Dashboard
 * Provides insights into filter performance and usage
 */

session_start();
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit();
}

require_once __DIR__ . '/config.php';
require_once 'filter_logger.php';

$logger = new FilterLogger();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filter System Monitor</title>
    <script src="assets/js/cdn.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; border-radius: 8px; padding: 20px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { font-size: 0.9em; opacity: 0.9; }
        .log-entry { background: #f8f9fa; border-left: 4px solid #007bff; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .error-entry { border-left-color: #dc3545; background: #f8d7da; }
        .performance-entry { border-left-color: #28a745; background: #d4edda; }
        .table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        .table th, .table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background-color: #f8f9fa; font-weight: 600; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
        .nav { background: #343a40; color: white; padding: 10px 20px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .nav a { color: #adb5bd; text-decoration: none; margin-right: 20px; }
        .nav a:hover { color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="nav">
                <h1 style="margin: 0; display: inline-block;">Filter System Monitor</h1>
                <a href="dashboard.php">← Back to Dashboard</a>
                <button class="refresh-btn" onclick="location.reload()" style="float: right;">Refresh</button>
            </div>
            
            <?php
            try {
                // Get performance statistics
                $stats = $logger->getPerformanceStats(24);
                $errors = $logger->getErrorSummary(24);
                
                echo "<div class='stats-grid'>";
                
                echo "<div class='stat-card'>";
                echo "<div class='stat-value'>" . number_format($stats['total_requests']) . "</div>";
                echo "<div class='stat-label'>Total Requests (24h)</div>";
                echo "</div>";
                
                echo "<div class='stat-card'>";
                echo "<div class='stat-value'>" . $stats['average_response_time'] . "ms</div>";
                echo "<div class='stat-label'>Average Response Time</div>";
                echo "</div>";
                
                echo "<div class='stat-card'>";
                echo "<div class='stat-value'>" . $stats['slowest_operation'] . "ms</div>";
                echo "<div class='stat-label'>Slowest Operation</div>";
                echo "</div>";
                
                echo "<div class='stat-card'>";
                echo "<div class='stat-value'>" . number_format($errors['total_errors']) . "</div>";
                echo "<div class='stat-label'>Total Errors (24h)</div>";
                echo "</div>";
                
                echo "</div>";
                
                // Operations breakdown
                if (!empty($stats['operations_breakdown'])) {
                    echo "<h2>Operations Performance</h2>";
                    echo "<table class='table'>";
                    echo "<thead><tr><th>Operation</th><th>Count</th><th>Avg Time (ms)</th><th>Min Time (ms)</th><th>Max Time (ms)</th></tr></thead>";
                    echo "<tbody>";
                    
                    foreach ($stats['operations_breakdown'] as $operation => $data) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($operation) . "</td>";
                        echo "<td>" . number_format($data['count']) . "</td>";
                        echo "<td>" . $data['avg_time'] . "</td>";
                        echo "<td>" . $data['min_time'] . "</td>";
                        echo "<td>" . $data['max_time'] . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                }
                
                // Error breakdown
                if (!empty($errors['error_types'])) {
                    echo "<h2>Error Types</h2>";
                    echo "<table class='table'>";
                    echo "<thead><tr><th>Error Type</th><th>Count</th></tr></thead>";
                    echo "<tbody>";
                    
                    foreach ($errors['error_types'] as $type => $count) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($type) . "</td>";
                        echo "<td>" . number_format($count) . "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                }
                
                // Recent operations
                echo "<h2>Recent Operations</h2>";
                $recent_logs = $logger->getRecentLogs('operations', 20);
                
                if (!empty($recent_logs)) {
                    foreach ($recent_logs as $log) {
                        $class = 'log-entry';
                        if (isset($log['execution_time_ms']) && $log['execution_time_ms'] > 500) {
                            $class .= ' performance-entry';
                        }
                        
                        echo "<div class='$class'>";
                        echo "<strong>" . htmlspecialchars($log['operation']) . "</strong> ";
                        echo "at " . htmlspecialchars($log['timestamp']);
                        
                        if (isset($log['execution_time_ms'])) {
                            echo " - " . $log['execution_time_ms'] . "ms";
                        }
                        
                        if (isset($log['user_id'])) {
                            echo " (User: " . $log['user_id'] . ")";
                        }
                        
                        if (isset($log['filters']) && !empty($log['filters'])) {
                            echo "<br><small>Filters: " . htmlspecialchars(json_encode($log['filters'])) . "</small>";
                        }
                        
                        if (isset($log['options_count'])) {
                            echo "<br><small>Options returned: " . $log['options_count'] . "</small>";
                        }
                        
                        echo "</div>";
                    }
                } else {
                    echo "<p>No recent operations logged.</p>";
                }
                
                // Recent errors
                if (!empty($errors['recent_errors'])) {
                    echo "<h2>Recent Errors</h2>";
                    
                    foreach ($errors['recent_errors'] as $error) {
                        echo "<div class='log-entry error-entry'>";
                        echo "<strong>Error:</strong> " . htmlspecialchars($error['error_message']);
                        echo " at " . htmlspecialchars($error['timestamp']);
                        
                        if (isset($error['context']) && !empty($error['context'])) {
                            echo "<br><small>Context: " . htmlspecialchars(json_encode($error['context'])) . "</small>";
                        }
                        
                        echo "</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='log-entry error-entry'>";
                echo "<strong>Monitor Error:</strong> " . htmlspecialchars($e->getMessage());
                echo "</div>";
            }
            ?>
            
            <h2>System Information</h2>
            <table class="table">
                <tr><th>PHP Version</th><td><?php echo PHP_VERSION; ?></td></tr>
                <tr><th>Memory Usage</th><td><?php echo round(memory_get_usage() / 1024 / 1024, 2); ?> MB</td></tr>
                <tr><th>Peak Memory</th><td><?php echo round(memory_get_peak_usage() / 1024 / 1024, 2); ?> MB</td></tr>
                <tr><th>Server Time</th><td><?php echo date('Y-m-d H:i:s'); ?></td></tr>
                <tr><th>Log Directory</th><td><?php echo __DIR__ . '/logs'; ?></td></tr>
                <tr><th>Log Files</th><td>
                    <?php
                    $log_files = [
                        'filter_operations.log',
                        'filter_performance.log', 
                        'filter_errors.log',
                        'filter_api.log'
                    ];
                    
                    foreach ($log_files as $file) {
                        $path = __DIR__ . '/logs/' . $file;
                        if (file_exists($path)) {
                            $size = round(filesize($path) / 1024, 2);
                            echo $file . " ({$size} KB)<br>";
                        } else {
                            echo $file . " (not found)<br>";
                        }
                    }
                    ?>
                </td></tr>
            </table>
            
            <h2>Test Links</h2>
            <p>Use these links to test the filter system:</p>
            <ul>
                <li><a href="test/test_filter_validation.php" target="_blank">Run Filter Validation Tests</a></li>
                <li><a href="filter_options.php?action=get_options&filter_type=sentiment" target="_blank">Test Sentiment Options API</a></li>
                <li><a href="filter_options.php?action=validate_combination&sentiment=Positive" target="_blank">Test Filter Validation API</a></li>
                <li><a href="data.php?type=validate-filter-combination&sentiment=Positive" target="_blank">Test Data.php Validation</a></li>
            </ul>
            
            <h2>Maintenance Actions</h2>
            <p>
                <button class="refresh-btn" onclick="clearCache()">Clear Filter Cache</button>
                <button class="refresh-btn" onclick="cleanLogs()">Clean Old Logs</button>
            </p>
        </div>
    </div>
    
    <script>
        function clearCache() {
            if (confirm('Are you sure you want to clear the filter cache?')) {
                fetch('filter_options.php?action=clear_cache', {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        alert('Cache cleared successfully');
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error clearing cache: ' + error);
                    });
            }
        }
        
        function cleanLogs() {
            if (confirm('Are you sure you want to clean old log files? This will remove logs older than 30 days.')) {
                fetch('filter_options.php?action=clean_logs', {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        alert('Logs cleaned successfully');
                        location.reload();
                    })
                    .catch(error => {
                        alert('Error cleaning logs: ' + error);
                    });
            }
        }
        
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
