<?php
/**
 * Filter Logger Service
 * Provides comprehensive logging for filter operations, performance metrics, and debugging
 */

class FilterLogger {
    private $log_file;
    private $performance_log_file;
    private $error_log_file;
    
    public function __construct() {
        $log_dir = __DIR__ . '/logs';
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        $this->log_file = $log_dir . '/filter_operations.log';
        $this->performance_log_file = $log_dir . '/filter_performance.log';
        $this->error_log_file = $log_dir . '/filter_errors.log';
    }
    
    /**
     * Log filter validation operations
     */
    public function logFilterValidation($user_id, $filters, $has_data, $execution_time, $query = null) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'filter_validation',
            'user_id' => $user_id,
            'filters' => $filters,
            'has_data' => $has_data,
            'execution_time_ms' => round($execution_time * 1000, 2),
            'query' => $query
        ];
        
        $this->writeLog($this->log_file, $log_entry);
        $this->writePerformanceLog($execution_time, 'filter_validation', $filters);
    }
    
    /**
     * Log filter options requests
     */
    public function logFilterOptionsRequest($user_id, $filter_type, $current_filters, $options_count, $execution_time) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'filter_options_request',
            'user_id' => $user_id,
            'filter_type' => $filter_type,
            'current_filters' => $current_filters,
            'options_count' => $options_count,
            'execution_time_ms' => round($execution_time * 1000, 2)
        ];
        
        $this->writeLog($this->log_file, $log_entry);
        $this->writePerformanceLog($execution_time, 'filter_options_' . $filter_type, $current_filters);
    }
    
    /**
     * Log batch filter requests
     */
    public function logBatchFilterRequest($user_id, $filter_types, $current_filters, $execution_time) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'batch_filter_request',
            'user_id' => $user_id,
            'filter_types' => $filter_types,
            'current_filters' => $current_filters,
            'execution_time_ms' => round($execution_time * 1000, 2)
        ];
        
        $this->writeLog($this->log_file, $log_entry);
        $this->writePerformanceLog($execution_time, 'batch_filter_request', $current_filters);
    }
    
    /**
     * Log filter state changes
     */
    public function logFilterStateChange($user_id, $filter_type, $old_value, $new_value, $resulting_filters) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'filter_state_change',
            'user_id' => $user_id,
            'filter_type' => $filter_type,
            'old_value' => $old_value,
            'new_value' => $new_value,
            'resulting_filters' => $resulting_filters
        ];
        
        $this->writeLog($this->log_file, $log_entry);
    }
    
    /**
     * Log AJAX filter update requests
     */
    public function logAjaxFilterUpdate($user_id, $request_data, $response_data, $execution_time) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'ajax_filter_update',
            'user_id' => $user_id,
            'request_data' => $request_data,
            'response_data' => $response_data,
            'execution_time_ms' => round($execution_time * 1000, 2)
        ];
        
        $this->writeLog($this->log_file, $log_entry);
        $this->writePerformanceLog($execution_time, 'ajax_filter_update', $request_data);
    }
    
    /**
     * Log cache operations
     */
    public function logCacheOperation($operation, $cache_key, $hit = null) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'cache_' . $operation,
            'cache_key' => $cache_key,
            'cache_hit' => $hit
        ];
        
        $this->writeLog($this->log_file, $log_entry);
    }
    
    /**
     * Log cache clear operations
     */
    public function logCacheClear() {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'cache_clear'
        ];
        
        $this->writeLog($this->log_file, $log_entry);
    }
    
    /**
     * Log errors
     */
    public function logError($error_message, $context = []) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error_message' => $error_message,
            'context' => $context,
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS)
        ];
        
        $this->writeLog($this->error_log_file, $log_entry);
    }
    
    /**
     * Log performance metrics
     */
    private function writePerformanceLog($execution_time, $operation, $context = []) {
        $performance_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => $operation,
            'execution_time_ms' => round($execution_time * 1000, 2),
            'context_size' => count($context),
            'memory_usage_mb' => round(memory_get_usage() / 1024 / 1024, 2),
            'peak_memory_mb' => round(memory_get_peak_usage() / 1024 / 1024, 2)
        ];
        
        $this->writeLog($this->performance_log_file, $performance_entry);
    }
    
    /**
     * Write log entry to file
     */
    private function writeLog($log_file, $log_entry) {
        $log_line = json_encode($log_entry) . "\n";
        file_put_contents($log_file, $log_line, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Get recent log entries
     */
    public function getRecentLogs($log_type = 'operations', $limit = 100) {
        $log_file = $this->log_file;
        
        switch ($log_type) {
            case 'performance':
                $log_file = $this->performance_log_file;
                break;
            case 'errors':
                $log_file = $this->error_log_file;
                break;
        }
        
        if (!file_exists($log_file)) {
            return [];
        }
        
        $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $recent_lines = array_slice($lines, -$limit);
        
        $logs = [];
        foreach ($recent_lines as $line) {
            $decoded = json_decode($line, true);
            if ($decoded) {
                $logs[] = $decoded;
            }
        }
        
        return array_reverse($logs); // Most recent first
    }
    
    /**
     * Get performance statistics
     */
    public function getPerformanceStats($hours = 24) {
        $logs = $this->getRecentLogs('performance', 10000);
        $cutoff_time = date('Y-m-d H:i:s', strtotime("-$hours hours"));
        
        $filtered_logs = array_filter($logs, function($log) use ($cutoff_time) {
            return $log['timestamp'] >= $cutoff_time;
        });
        
        if (empty($filtered_logs)) {
            return [
                'total_requests' => 0,
                'average_response_time' => 0,
                'slowest_operation' => null,
                'fastest_operation' => null,
                'operations_breakdown' => []
            ];
        }
        
        $execution_times = array_column($filtered_logs, 'execution_time_ms');
        $operations = array_column($filtered_logs, 'operation');
        
        $operations_breakdown = [];
        foreach ($filtered_logs as $log) {
            $op = $log['operation'];
            if (!isset($operations_breakdown[$op])) {
                $operations_breakdown[$op] = [
                    'count' => 0,
                    'total_time' => 0,
                    'avg_time' => 0,
                    'min_time' => PHP_FLOAT_MAX,
                    'max_time' => 0
                ];
            }
            
            $operations_breakdown[$op]['count']++;
            $operations_breakdown[$op]['total_time'] += $log['execution_time_ms'];
            $operations_breakdown[$op]['min_time'] = min($operations_breakdown[$op]['min_time'], $log['execution_time_ms']);
            $operations_breakdown[$op]['max_time'] = max($operations_breakdown[$op]['max_time'], $log['execution_time_ms']);
        }
        
        // Calculate averages
        foreach ($operations_breakdown as $op => &$stats) {
            $stats['avg_time'] = round($stats['total_time'] / $stats['count'], 2);
        }
        
        return [
            'total_requests' => count($filtered_logs),
            'average_response_time' => round(array_sum($execution_times) / count($execution_times), 2),
            'slowest_operation' => max($execution_times),
            'fastest_operation' => min($execution_times),
            'operations_breakdown' => $operations_breakdown
        ];
    }
    
    /**
     * Get error summary
     */
    public function getErrorSummary($hours = 24) {
        $logs = $this->getRecentLogs('errors', 1000);
        $cutoff_time = date('Y-m-d H:i:s', strtotime("-$hours hours"));
        
        $filtered_logs = array_filter($logs, function($log) use ($cutoff_time) {
            return $log['timestamp'] >= $cutoff_time;
        });
        
        $error_types = [];
        foreach ($filtered_logs as $log) {
            $error_type = $this->categorizeError($log['error_message']);
            if (!isset($error_types[$error_type])) {
                $error_types[$error_type] = 0;
            }
            $error_types[$error_type]++;
        }
        
        return [
            'total_errors' => count($filtered_logs),
            'error_types' => $error_types,
            'recent_errors' => array_slice($filtered_logs, 0, 10)
        ];
    }
    
    /**
     * Categorize error messages
     */
    private function categorizeError($error_message) {
        if (strpos($error_message, 'database') !== false || strpos($error_message, 'SQL') !== false) {
            return 'Database Error';
        } elseif (strpos($error_message, 'validation') !== false) {
            return 'Validation Error';
        } elseif (strpos($error_message, 'cache') !== false) {
            return 'Cache Error';
        } elseif (strpos($error_message, 'filter') !== false) {
            return 'Filter Error';
        } else {
            return 'General Error';
        }
    }
    
    /**
     * Clean old log files (keep last 30 days)
     */
    public function cleanOldLogs($days = 30) {
        $cutoff_time = date('Y-m-d H:i:s', strtotime("-$days days"));
        
        $log_files = [$this->log_file, $this->performance_log_file, $this->error_log_file];
        
        foreach ($log_files as $log_file) {
            if (!file_exists($log_file)) {
                continue;
            }
            
            $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $filtered_lines = [];
            
            foreach ($lines as $line) {
                $decoded = json_decode($line, true);
                if ($decoded && $decoded['timestamp'] >= $cutoff_time) {
                    $filtered_lines[] = $line;
                }
            }
            
            file_put_contents($log_file, implode("\n", $filtered_lines) . "\n");
        }
    }
}
