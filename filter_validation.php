<?php
/**
 * Filter Validation Service
 * Provides centralized validation for filter combinations and data existence checking
 */

require_once __DIR__ . '/config.php';
require_once 'DatabaseInteraction.php';
require_once 'filter_logger.php';

class FilterValidation {
    private $db;
    private $logger;
    private $cache = [];
    private $cache_ttl = 300; // 5 minutes cache TTL
    
    public function __construct() {
        $this->db = new DatabaseInteraction();
        $this->logger = new FilterLogger();
    }
    
    /**
     * Validate if a filter combination has any data
     */
    public function validateFilterCombination($user_id, $filters = []) {
        $cache_key = $this->generateCacheKey('validate', $user_id, $filters);
        
        // Check cache first
        if ($this->isCacheValid($cache_key)) {
            return $this->cache[$cache_key]['data'];
        }
        
        $start_time = microtime(true);
        
        try {
            $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = :user_id";
            $params = [':user_id' => $user_id];
            
            // Add filter conditions
            $query .= $this->buildFilterConditions($filters, $params);
            
            $conn = $this->db->connect();
            $stmt = $conn->prepare($query);
            $stmt->execute($params);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $has_data = $result['count'] > 0;
            $execution_time = microtime(true) - $start_time;
            
            // Cache the result
            $this->cache[$cache_key] = [
                'data' => $has_data,
                'timestamp' => time()
            ];
            
            // Log the validation
            $this->logger->logFilterValidation($user_id, $filters, $has_data, $execution_time, $query);
            
            return $has_data;
            
        } catch (Exception $e) {
            $this->logger->logError("Filter validation error: " . $e->getMessage(), $filters);
            return false;
        }
    }
    
    /**
     * Get available options for a specific filter based on current filter state
     */
    public function getAvailableFilterOptions($user_id, $filter_type, $current_filters = []) {
        $cache_key = $this->generateCacheKey('options', $user_id, array_merge($current_filters, ['type' => $filter_type]));
        
        // Check cache first
        if ($this->isCacheValid($cache_key)) {
            return $this->cache[$cache_key]['data'];
        }
        
        $start_time = microtime(true);
        
        try {
            $options = [];
            
            switch ($filter_type) {
                case 'domain_category':
                    $options = $this->getAvailableDomainCategories($user_id, $current_filters);
                    break;
                case 'data_id':
                    $options = $this->getAvailableDataIds($user_id, $current_filters);
                    break;
                case 'sentiment':
                    $options = $this->getAvailableSentiments($user_id, $current_filters);
                    break;
                case 'product_type':
                    $options = $this->getAvailableProductTypes($user_id, $current_filters);
                    break;
                case 'channel_type':
                    $options = $this->getAvailableChannelTypes($user_id, $current_filters);
                    break;
                case 'team':
                    $options = $this->getAvailableTeams($user_id, $current_filters);
                    break;
                case 'resolution_status':
                    $options = $this->getAvailableResolutionStatuses($user_id, $current_filters);
                    break;
                case 'start_date':
                case 'end_date':
                    // Date filters don't have predefined options, return empty array
                    $options = [];
                    break;
                default:
                    throw new Exception("Unknown filter type: $filter_type");
            }
            
            $execution_time = microtime(true) - $start_time;
            
            // Cache the result
            $this->cache[$cache_key] = [
                'data' => $options,
                'timestamp' => time()
            ];
            
            // Log the operation
            $this->logger->logFilterOptionsRequest($user_id, $filter_type, $current_filters, count($options), $execution_time);
            
            return $options;
            
        } catch (Exception $e) {
            $this->logger->logError("Filter options error for $filter_type: " . $e->getMessage(), $current_filters);
            return [];
        }
    }
    
    /**
     * Get all available filter options for multiple filters at once
     */
    public function getBatchFilterOptions($user_id, $filter_types, $current_filters = []) {
        $start_time = microtime(true);
        $results = [];
        
        foreach ($filter_types as $filter_type) {
            $results[$filter_type] = $this->getAvailableFilterOptions($user_id, $filter_type, $current_filters);
        }
        
        $execution_time = microtime(true) - $start_time;
        $this->logger->logBatchFilterRequest($user_id, $filter_types, $current_filters, $execution_time);
        
        return $results;
    }
    
    /**
     * Build filter conditions for SQL queries
     */
    private function buildFilterConditions($filters, &$params) {
        $conditions = [];
        
        if (!empty($filters['data_id']) && $filters['data_id'] !== 'all') {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $filters['data_id'];
        }
        
        if (!empty($filters['sentiment']) && $filters['sentiment'] !== 'all') {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $filters['sentiment'];
        }
        
        if (!empty($filters['domain_category']) && $filters['domain_category'] !== 'all') {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $filters['domain_category'];
        }
        
        if (!empty($filters['start_date'])) {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }
        
        if (!empty($filters['partner']) && $filters['partner'] !== 'all') {
            $conditions[] = "partner = :partner";
            $params[':partner'] = $filters['partner'];
        }

        if (!empty($filters['lob']) && $filters['lob'] !== 'all') {
            $conditions[] = "lob = :lob";
            $params[':lob'] = $filters['lob'];
        }

        if (!empty($filters['dummy_1']) && $filters['dummy_1'] !== 'all') {
            $conditions[] = "dummy_1 = :dummy_1";
            $params[':dummy_1'] = $filters['dummy_1'];
        }

        if (!empty($filters['dummy_5']) && $filters['dummy_5'] !== 'all') {
            $conditions[] = "dummy_5 = :dummy_5";
            $params[':dummy_5'] = $filters['dummy_5'];
        }
        
        return empty($conditions) ? '' : ' AND ' . implode(' AND ', $conditions);
    }
    
    /**
     * Get available domain categories based on current filters
     */
    private function getAvailableDomainCategories($user_id, $current_filters) {
        // Exclude domain_category from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['domain_category']);
        
        $query = "SELECT DISTINCT domain_category FROM analyzed_comments 
                  WHERE user_id = :user_id AND domain_category IS NOT NULL AND domain_category != ''";
        $params = [':user_id' => $user_id];
        
        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY domain_category";
        
        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get available data IDs based on current filters
     */
    private function getAvailableDataIds($user_id, $current_filters) {
        // Exclude data_id from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['data_id']);
        
        $query = "SELECT DISTINCT data_id FROM analyzed_comments 
                  WHERE user_id = :user_id";
        $params = [':user_id' => $user_id];
        
        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY data_id DESC";
        
        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get available sentiments based on current filters
     */
    private function getAvailableSentiments($user_id, $current_filters) {
        // Exclude sentiment from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['sentiment']);
        
        $query = "SELECT DISTINCT sentiment FROM analyzed_comments 
                  WHERE user_id = :user_id AND sentiment IS NOT NULL AND sentiment != ''";
        $params = [':user_id' => $user_id];
        
        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY CASE sentiment 
                      WHEN 'Positive' THEN 1 
                      WHEN 'Neutral' THEN 2 
                      WHEN 'Negative' THEN 3 
                      ELSE 4 END";
        
        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get available product types (partner) based on current filters
     */
    private function getAvailableProductTypes($user_id, $current_filters) {
        // Exclude partner from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['partner']);

        $query = "SELECT DISTINCT partner FROM analyzed_comments
                  WHERE user_id = :user_id AND partner IS NOT NULL AND partner != ''";
        $params = [':user_id' => $user_id];

        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY partner";

        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Get available channel types (lob) based on current filters
     */
    private function getAvailableChannelTypes($user_id, $current_filters) {
        // Exclude lob from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['lob']);

        $query = "SELECT DISTINCT lob FROM analyzed_comments
                  WHERE user_id = :user_id AND lob IS NOT NULL AND lob != ''";
        $params = [':user_id' => $user_id];

        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY lob";

        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Get available teams (dummy_1) based on current filters
     */
    private function getAvailableTeams($user_id, $current_filters) {
        // Exclude dummy_1 from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['dummy_1']);

        $query = "SELECT DISTINCT dummy_1 FROM analyzed_comments
                  WHERE user_id = :user_id AND dummy_1 IS NOT NULL AND dummy_1 != ''";
        $params = [':user_id' => $user_id];

        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY dummy_1";

        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Get available resolution statuses (dummy_5) based on current filters
     */
    private function getAvailableResolutionStatuses($user_id, $current_filters) {
        // Exclude dummy_5 from current filters to avoid circular dependency
        $filters = $current_filters;
        unset($filters['dummy_5']);

        $query = "SELECT DISTINCT dummy_5 FROM analyzed_comments
                  WHERE user_id = :user_id AND dummy_5 IS NOT NULL AND dummy_5 != ''";
        $params = [':user_id' => $user_id];

        $query .= $this->buildFilterConditions($filters, $params);
        $query .= " ORDER BY dummy_5";

        $conn = $this->db->connect();
        $stmt = $conn->prepare($query);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Generate cache key for filter operations
     */
    private function generateCacheKey($operation, $user_id, $filters) {
        return md5($operation . '_' . $user_id . '_' . serialize($filters));
    }
    
    /**
     * Check if cache entry is valid
     */
    private function isCacheValid($cache_key) {
        return isset($this->cache[$cache_key]) && 
               (time() - $this->cache[$cache_key]['timestamp']) < $this->cache_ttl;
    }
    
    /**
     * Clear cache (useful for testing or when data changes)
     */
    public function clearCache() {
        $this->cache = [];
        $this->logger->logCacheClear();
    }
}
