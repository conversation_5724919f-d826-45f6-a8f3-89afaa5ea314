<?php
session_start();
require_once __DIR__ . '/config.php';
// Get the request type
$type = isset($_GET['type']) ? htmlspecialchars($_GET['type']) : '';

// Skip session check only for test endpoint
if ($type !== 'test-endpoint') {
    // Check if user is logged in
    if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Session expired or invalid']);
        exit();
    }
}
require_once __DIR__ . '/config.php';
require_once 'DatabaseInteraction.php';
$db = new DatabaseInteraction();
$conn = $db->connect();

if (!$conn) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// Sanitize and decode parameters
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0; // Default to 0 for test endpoint
$data_id = isset($_GET['data_id']) && $_GET['data_id'] !== 'all' ? htmlspecialchars($_GET['data_id']) : null;
$main_driver = isset($_GET['main_driver']) ? htmlspecialchars($_GET['main_driver']) : null;
$sub_driver = isset($_GET['sub_driver']) ? htmlspecialchars($_GET['sub_driver']) : null;
$sentiment = isset($_GET['sentiment']) && $_GET['sentiment'] !== 'all' ? htmlspecialchars($_GET['sentiment']) : null;

// Process domain_category parameter
if (isset($_GET['domain_category'])) {
    if ($_GET['domain_category'] === 'all') {
        $domain_category = null; // Set to null for 'all' value
    } else {
        // Decode and sanitize the domain_category value
        $domain_category = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
    }
} else {
    $domain_category = null; // Default value if not set
}

// Process new filter parameters
$start_date = isset($_GET['start_date']) && $_GET['start_date'] !== '' ? htmlspecialchars($_GET['start_date']) : null;
$end_date = isset($_GET['end_date']) && $_GET['end_date'] !== '' ? htmlspecialchars($_GET['end_date']) : null;
$product_type = isset($_GET['product_type']) && $_GET['product_type'] !== 'all' ? htmlspecialchars($_GET['product_type']) : null;
$channel_type = isset($_GET['channel_type']) && $_GET['channel_type'] !== 'all' ? htmlspecialchars($_GET['channel_type']) : null;
$team = isset($_GET['team']) && $_GET['team'] !== 'all' ? htmlspecialchars($_GET['team']) : null;
$resolution_status = isset($_GET['resolution_status']) && $_GET['resolution_status'] !== 'all' ? htmlspecialchars($_GET['resolution_status']) : null;

// Validate data_id if provided
if ($data_id === false) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid data_id format']);
    exit();
}

try {
    switch ($type) {
        case 'comments':
            $total_comments = $db->getTotalComments($data_id, $user_id, $sentiment, $domain_category, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status);
            echo json_encode(['total' => $total_comments]);
            break;

        case 'main-drivers':
            $main_drivers_count = $db->getMainDriversCount($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($main_drivers_count);
            break;

        case 'sub-drivers':
            $sub_drivers_count = $db->getSubDriversCount($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($sub_drivers_count);
            break;
        case 'main-drivers-sentiment':
            $main_drivers_sentiment = $db->getMainDriversSentiment($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($main_drivers_sentiment);
            break;
        case 'sub-drivers-sentiment':
            $sub_drivers_sentiment = $db->getSubDriversSentiment($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($sub_drivers_sentiment);
            break;
        case 'detailed-sentiments':
            $detailed_sentiments = $db->getDetailedSentimentsData($user_id, $data_id, $sentiment, $domain_category);
            echo json_encode($detailed_sentiments);
            break;
        case 'sentiments':
            $sentiments_count = $db->getSentimentsCount($data_id, $user_id, $sentiment, $domain_category, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status);
            echo json_encode($sentiments_count);
            break;
        case 'sentiments-across-main-drivers':
            $sentiments_across_main_drivers_count = $db->getSentimentsAcrossMainDriversCount($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($sentiments_across_main_drivers_count);
            break;
        case 'sub-drivers-contribution':
            $sub_drivers_contribution = $db->getSubDriversContribution($data_id, $user_id, $sentiment, $domain_category,
                                                                      $start_date, $end_date, $product_type, $channel_type,
                                                                      $team, $resolution_status);
            echo json_encode($sub_drivers_contribution);
            break;
        case 'sub-drivers-by-main-driver':
            $sub_drivers_by_main_driver = $db->getSubDriversByMainDriver($data_id, $user_id, $main_driver, $sentiment, $domain_category);
            echo json_encode($sub_drivers_by_main_driver);
            break;
        case 'sub-drivers-table':
            $sub_drivers_table = $db->getSubDriversTableData($data_id, $user_id, $main_driver, $sub_driver, $sentiment, $domain_category);
            echo json_encode($sub_drivers_table);
            break;
        case 'word-cloud':
            $word_cloud_data = $db->getWordCloudData($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($word_cloud_data);
            break;

        case 'historical-sentiments':
            $historical_sentiments = $db->getHistoricalSentiments($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($historical_sentiments);
            break;
        case 'csat-impact':
            $csatImpact = $db->getCsatImpact($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($csatImpact);
            break;
        case 'nps-impact':
            $npsImpact = $db->getNpsImpact($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($npsImpact);
            break;
        case 'time-series-sentiments':
            $days = isset($_GET['days']) ? $_GET['days'] : 7;
            $view_type = isset($_GET['view_type']) ? $_GET['view_type'] : 'daily';

            // Convert 'all' to a large number to get all data
            if ($days === 'all') {
                $days = 3650; // ~10 years
            } else {
                $days = intval($days);
            }

            $time_series_sentiments = $db->getTimeSeriesSentiments($data_id, $user_id, $sentiment, $domain_category, $days, $view_type);
            if (!is_array($time_series_sentiments)) {
                header('Content-Type: application/json');
                echo json_encode(['error' => 'Invalid data structure for time series sentiments']);
                exit();
            }
            echo json_encode($time_series_sentiments);
            break;
        case 'top-positive-sub-drivers':
            $topPositiveSubDrivers = $db->getTopPositiveSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topPositiveSubDrivers);
            break;
        case 'top-negative-sub-drivers':
            $topNegativeSubDrivers = $db->getTopNegativeSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topNegativeSubDrivers);
            break;
        case 'top-csat-impact-sub-drivers':
            $topCsatImpactSubDrivers = $db->getTopCsatImpactSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topCsatImpactSubDrivers);
            break;
        case 'top-nps-impact-sub-drivers':
            $topNpsImpactSubDrivers = $db->getTopNpsImpactSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topNpsImpactSubDrivers);
            break;
        case 'top-nps-promoter-sub-drivers':
            $topNpsPromoterSubDrivers = $db->getTopNpsPromoterSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topNpsPromoterSubDrivers);
            break;
        case 'top-nps-detractor-sub-drivers':
            $topNpsDetractorSubDrivers = $db->getTopNpsDetractorSubDrivers($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($topNpsDetractorSubDrivers);
            break;
        case 'feedback-comments-summary':
            $feedbackCommentsSummary = $db->getFeedbackCommentsSummary($data_id, $user_id, $sentiment, $domain_category);
            echo json_encode($feedbackCommentsSummary);
            break;
        case 'data-ids':
            // Process domain_category parameter for data-ids case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
                echo json_encode($db->getDataIdsByDomainCategory($user_id, $domain_cat));
            } else {
                echo json_encode($db->getDataIdsByUserId($user_id));
            }
            break;

        case 'domain-categories':
            if (isset($_GET['data_id']) && $_GET['data_id'] !== 'all') {
                $dataId = $_GET['data_id'];
                // When fetching domain categories for a specific data_id, also check the current domain category
                $currentDomain = isset($_GET['current_domain']) ? urldecode($_GET['current_domain']) : null;
                echo json_encode($db->getDomainsByDataId($user_id, $dataId, $currentDomain));
            } else {
                echo json_encode($db->getUniqueDomainCategories($user_id));
            }
            break;

        case 'filtered-data':
            // Process parameters for filtered-data case
            $data_id_local = isset($_GET['data_id']) && $_GET['data_id'] !== 'all' ?
                htmlspecialchars($_GET['data_id']) : null;

            $sentiment_local = isset($_GET['sentiment']) && $_GET['sentiment'] !== 'all' ?
                htmlspecialchars($_GET['sentiment']) : null;

            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            $result = $db->getFilteredData($user_id, $data_id_local, $sentiment_local, $domain_cat);
            echo json_encode($result);
            break;

        case 'sentiment-drivers':
            // Process parameters for sentiment-drivers case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            $data_id_local = isset($_GET['data_id']) && $_GET['data_id'] !== 'all' ?
                htmlspecialchars($_GET['data_id']) : null;

            $sentiment_local = isset($_GET['sentiment']) && $_GET['sentiment'] !== 'all' ?
                htmlspecialchars($_GET['sentiment']) : null;

            $drivers_data = $db->getSentimentDrivers($data_id_local, $user_id, $sentiment_local, $domain_cat);
            echo json_encode($drivers_data);
            break;

        case 'test-endpoint':
            echo json_encode(['status' => 'success', 'message' => 'Test endpoint is working']);
            break;

        case 'debug-charts':
            // Disabled debug-charts endpoint to prevent dummy data from being used
            echo json_encode(['error' => 'Debug charts endpoint has been disabled. Please use real data endpoints.']);
            break;

        case 'lob-sentiment':
            error_log("Processing lob-sentiment request: data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");
            // Process domain_category parameter for lob-sentiment case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            // Debug query to check if there's any data
            $debug_query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = $user_id AND lob IS NOT NULL AND lob != ''";
            $debug_result = $conn->query($debug_query)->fetch(PDO::FETCH_ASSOC);
            error_log("DEBUG: Total records with non-empty LOB: " . $debug_result['count']);

            $lobSentiment = $db->getLobSentiment($data_id, $user_id, $sentiment, $domain_cat,
                                                $start_date, $end_date, $product_type, $channel_type,
                                                $team, $resolution_status);
            error_log("LOB Sentiment result: " . json_encode($lobSentiment));
            echo json_encode($lobSentiment);
            break;

        case 'vendor-sentiment':
            error_log("Processing vendor-sentiment request: data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");
            // Process domain_category parameter for vendor-sentiment case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            // Debug query to check if there's any data
            $debug_query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = $user_id AND vendor IS NOT NULL AND vendor != ''";
            $debug_result = $conn->query($debug_query)->fetch(PDO::FETCH_ASSOC);
            error_log("DEBUG: Total records with non-empty vendor: " . $debug_result['count']);

            $vendorSentiment = $db->getVendorSentiment($data_id, $user_id, $sentiment, $domain_cat,
                                                      $start_date, $end_date, $product_type, $channel_type,
                                                      $team, $resolution_status);
            error_log("Vendor Sentiment result: " . json_encode($vendorSentiment));
            echo json_encode($vendorSentiment);
            break;

        case 'location-sentiment':
            error_log("Processing location-sentiment request: data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");
            // Process domain_category parameter for location-sentiment case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            // Debug query to check if there's any data
            $debug_query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = $user_id AND location IS NOT NULL AND location != ''";
            $debug_result = $conn->query($debug_query)->fetch(PDO::FETCH_ASSOC);
            error_log("DEBUG: Total records with non-empty location: " . $debug_result['count']);

            $locationSentiment = $db->getLocationSentiment($data_id, $user_id, $sentiment, $domain_cat,
                                                          $start_date, $end_date, $product_type, $channel_type,
                                                          $team, $resolution_status);
            error_log("Location Sentiment result: " . json_encode($locationSentiment));
            echo json_encode($locationSentiment);
            break;

        case 'partner-sentiment':
            error_log("Processing partner-sentiment request: data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");
            // Process domain_category parameter for partner-sentiment case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            // Debug query to check if there's any data
            $debug_query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = $user_id AND partner IS NOT NULL AND partner != ''";
            $debug_result = $conn->query($debug_query)->fetch(PDO::FETCH_ASSOC);
            error_log("DEBUG: Total records with non-empty partner: " . $debug_result['count']);

            $partnerSentiment = $db->getPartnerSentiment($data_id, $user_id, $sentiment, $domain_cat,
                                                        $start_date, $end_date, $product_type, $channel_type,
                                                        $team, $resolution_status);
            error_log("Partner Sentiment result: " . json_encode($partnerSentiment));
            echo json_encode($partnerSentiment);
            break;

        case 'consolidated-summary':
            // Process domain_category parameter for consolidated-summary case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }
            $consolidatedSummary = $db->getConsolidatedSummary($data_id, $user_id, $domain_cat,
                                                               $start_date, $end_date, $product_type, $channel_type,
                                                               $team, $resolution_status);
            echo json_encode($consolidatedSummary);
            break;

        case 'dashboard-batch':
            // Performance optimization: Single API call for all dashboard data
            $days = isset($_GET['days']) ? $_GET['days'] : 7;
            $view_type = isset($_GET['view_type']) ? $_GET['view_type'] : 'daily';

            // Convert 'all' to a large number to get all data
            if ($days === 'all') {
                $days = 3650; // ~10 years
            } else {
                $days = intval($days);
            }

            // Process domain_category parameter for batch case
            $domain_cat = null;
            if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all') {
                $domain_cat = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
            }

            $batchData = [
                'commentsData' => ['total' => $db->getTotalComments($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status)],
                'sentimentsData' => $db->getSentimentsCount($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'historicalSentimentsData' => $db->getHistoricalSentiments($data_id, $user_id, $sentiment, $domain_cat),
                'timeSeriesData' => $db->getTimeSeriesSentiments($data_id, $user_id, $sentiment, $domain_cat, $days, $view_type, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'mainDriversData' => $db->getMainDriversCount($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'sentimentsAcrossDriversData' => $db->getSentimentsAcrossMainDriversCount($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'wordCloudData' => $db->getWordCloudData($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'mainDriversSentimentData' => $db->getMainDriversSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'subDriversSentimentData' => $db->getSubDriversSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'detailedSentimentsData' => $db->getDetailedSentimentsData($user_id, $data_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topPositiveSubDrivers' => $db->getTopPositiveSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topNegativeSubDrivers' => $db->getTopNegativeSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topCsatImpactSubDrivers' => $db->getTopCsatImpactSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topNpsImpactSubDrivers' => $db->getTopNpsImpactSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topNpsPromoterSubDrivers' => $db->getTopNpsPromoterSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'topNpsDetractorSubDrivers' => $db->getTopNpsDetractorSubDrivers($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'feedbackCommentsSummary' => $db->getFeedbackCommentsSummary($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'csatImpactData' => $db->getCsatImpact($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'npsImpactData' => $db->getNpsImpact($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'lobSentimentData' => $db->getLobSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'vendorSentimentData' => $db->getVendorSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'locationSentimentData' => $db->getLocationSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'partnerSentimentData' => $db->getPartnerSentiment($data_id, $user_id, $sentiment, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status),
                'consolidatedSummaryData' => $db->getConsolidatedSummary($data_id, $user_id, $domain_cat, $start_date, $end_date, $product_type, $channel_type, $team, $resolution_status)
            ];

            header('Content-Type: application/json');
            echo json_encode($batchData);
            break;

        // New endpoints for enhanced filter options
        case 'filter-options-partners':
            $partners = $db->getDistinctPartners($user_id);
            echo json_encode($partners);
            break;

        case 'filter-options-lobs':
            $lobs = $db->getDistinctLobs($user_id);
            echo json_encode($lobs);
            break;

        case 'filter-options-teams':
            $teams = $db->getDistinctTeams($user_id);
            echo json_encode($teams);
            break;

        case 'filter-options-resolution-statuses':
            $statuses = $db->getDistinctResolutionStatuses($user_id);
            echo json_encode($statuses);
            break;

        case 'filter-options-date-range':
            $dateRange = $db->getDateRange($user_id);
            echo json_encode($dateRange);
            break;

        default:
            error_log("Invalid request type: $type");
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Invalid request type']);
            break;
    }
    exit(); // Ensure no further output
} catch (Exception $e) {
    error_log("Error in data.php - Type: $type - Error: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode(['error' => 'An unexpected error occurred']);
    exit();
}
