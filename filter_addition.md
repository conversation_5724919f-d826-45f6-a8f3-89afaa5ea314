# Enhanced Filter System Implementation Reference Documentation

## 1. DatabaseInteraction.php Methods Updated

The following **23 methods** in `DatabaseInteraction.php` were updated to accept and process the 6 new filter parameters:

### Core Dashboard Methods (17 methods):
1. `getTimeSeriesSentiments`
2. `getMainDriversCount`
3. `getWordCloudData`
4. `getSentimentsAcrossMainDriversCount`
5. `getMainDriversSentiment`
6. `getSubDriversSentiment`
7. `getDetailedSentimentsData`
8. `getTopPositiveSubDrivers`
9. `getTopNegativeSubDrivers`
10. `getTopCsatImpactSubDrivers`
11. `getTopNpsImpactSubDrivers`
12. `getTopNpsPromoterSubDrivers`
13. `getTopNpsDetractorSubDrivers`
14. `getFeedbackCommentsSummary`
15. `getTopContributorFeedbackComments`
16. `getCsatImpact`
17. `getNpsImpact`

### Sentiment Distribution Methods (4 methods):
18. `getLobSentiment` - Channel Sentiment Distribution
19. `getVendorSentiment` - Vendor Sentiment Distribution
20. `getLocationSentiment` - Site Sentiment Distribution
21. `getPartnerSentiment` - Product Type Sentiment Distribution

### Additional Methods (2 methods):
22. `getSubDriversContribution` - L2 Drivers Distribution Across L1 Drivers
23. `getConsolidatedSummary` - Consolidated Summary

### Method Signature Pattern:
All methods were updated to include these 6 additional parameters:
```php
$start_date = null, $end_date = null, $product_type = null, 
$channel_type = null, $team = null, $resolution_status = null
```

---

## 2. JavaScript Functions Modified

The following JavaScript functions in `dashboard.php` were updated:

### Primary Functions Updated:
1. **`updateTable()`** - L2 Drivers Distribution table update function
   - Added collection of new filter parameters
   - Updated AJAX request to include all filter parameters
   - Changed from simple query string to URLSearchParams for better parameter handling

2. **`fetchData()`** - Main dashboard data fetching function
   - Already had new filter parameters implemented
   - Sends all filter data to `dashboard-batch` endpoint

3. **`fetchMainDrivers()`** - Main drivers dropdown population function
   - Already had new filter parameters implemented
   - Updates main driver options based on current filters

### Function Enhancement Pattern:
```javascript
// New filter parameter collection pattern added to functions:
const startDate = document.getElementById('startDateFilter').value;
const endDate = document.getElementById('endDateFilter').value;
const productType = document.getElementById('productTypeDropdown').value;
const channelType = document.getElementById('channelTypeDropdown').value;
const team = document.getElementById('teamDropdown').value;
const resolutionStatus = document.getElementById('resolutionStatusDropdown').value;
```

---

## 3. Event Listeners Added/Modified

### New Filter Control Event Listeners Added:
1. **`startDateFilter`** - Date range start input
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

2. **`endDateFilter`** - Date range end input
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

3. **`productTypeDropdown`** - Product type select dropdown
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

4. **`channelTypeDropdown`** - Channel type select dropdown
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

5. **`teamDropdown`** - Team select dropdown
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

6. **`resolutionStatusDropdown`** - Resolution status select dropdown
   - Event: `change`
   - Actions: `debouncedFetchData()`, `fetchMainDrivers()`, `updateTable()`

### Existing Filter Control Event Listeners Modified:
7. **`domainCategoryDropdown`** - Domain category select dropdown
   - Event: `change`
   - Actions: Added `updateTable()` to existing `debouncedFetchData()`, `fetchMainDrivers()`

8. **`dataIdDropdown`** - Data ID select dropdown
   - Event: `change`
   - Actions: Added `updateTable()` to existing `debouncedFetchData()`, `fetchMainDrivers()`

9. **`sentimentDropdown`** - Sentiment select dropdown
   - Event: `change`
   - Actions: Added `updateTable()` to existing `debouncedFetchData()`, `fetchMainDrivers()`

### Event Listener Pattern:
```javascript
document.getElementById('filterElementId').addEventListener('change', () => {
    if (isUpdatingFilters) return;
    console.log('Filter changed to:', document.getElementById('filterElementId').value);
    debouncedFetchData();      // Update main dashboard
    fetchMainDrivers();        // Update main drivers dropdown
    updateTable();             // Update L2 drivers table
});
```

---

## 4. data.php API Endpoints Updated

The following **7 API endpoints** in `data.php` were updated:

### Individual Component Endpoints:
1. **`sub-drivers-contribution`** - L2 Drivers Distribution table
2. **`lob-sentiment`** - Channel Sentiment Distribution
3. **`vendor-sentiment`** - Vendor Sentiment Distribution
4. **`location-sentiment`** - Site Sentiment Distribution
5. **`partner-sentiment`** - Product Type Sentiment Distribution
6. **`consolidated-summary`** - Consolidated Summary

### Batch Endpoint:
7. **`dashboard-batch`** - Main dashboard data batch endpoint
   - Already had new filter parameters implemented

### Parameter Extraction Pattern:
All endpoints extract the new filter parameters using:
```php
$start_date = $_GET['start_date'] ?? null;
$end_date = $_GET['end_date'] ?? null;
$product_type = $_GET['product_type'] ?? null;
$channel_type = $_GET['channel_type'] ?? null;
$team = $_GET['team'] ?? null;
$resolution_status = $_GET['resolution_status'] ?? null;
```

### Method Call Pattern:
All endpoints pass the new parameters to database methods:
```php
$result = $db->methodName($data_id, $user_id, $sentiment, $domain_cat,
                         $start_date, $end_date, $product_type, $channel_type,
                         $team, $resolution_status);
```

---

## 5. Filter Parameter Mapping

### Frontend to Backend Parameter Mapping:

| **Frontend Filter Name** | **JavaScript Variable** | **URL Parameter** | **Database Field** | **Description** |
|-------------------------|------------------------|-------------------|-------------------|-----------------|
| Date Range Start | `startDate` | `start_date` | `DATE(IFNULL(feedback_submit_date, created_at))` | Start date for date range filter |
| Date Range End | `endDate` | `end_date` | `DATE(IFNULL(feedback_submit_date, created_at))` | End date for date range filter |
| Product Type | `productType` | `product_type` | `partner` | Product type/partner filter |
| Channel Type | `channelType` | `channel_type` | `lob` | Line of business/channel filter |
| Team | `team` | `team` | `dummy_1` | Team assignment filter |
| Resolution Status | `resolutionStatus` | `resolution_status` | `dummy_5` | Resolution status filter |

### Frontend Control Element IDs:

| **Filter** | **HTML Element ID** | **Element Type** |
|-----------|-------------------|------------------|
| Date Range Start | `startDateFilter` | `<input type="date">` |
| Date Range End | `endDateFilter` | `<input type="date">` |
| Product Type | `productTypeDropdown` | `<select>` |
| Channel Type | `channelTypeDropdown` | `<select>` |
| Team | `teamDropdown` | `<select>` |
| Resolution Status | `resolutionStatusDropdown` | `<select>` |

### SQL Filter Condition Patterns:

| **Filter Type** | **SQL Condition Pattern** | **Parameter Binding** |
|----------------|---------------------------|----------------------|
| Date Range Start | `DATE(IFNULL(feedback_submit_date, created_at)) >= ?` | `$params[] = $start_date` |
| Date Range End | `DATE(IFNULL(feedback_submit_date, created_at)) <= ?` | `$params[] = $end_date` |
| Product Type | `partner = ?` | `$params[] = $product_type` |
| Channel Type | `lob = ?` | `$params[] = $channel_type` |
| Team | `dummy_1 = ?` | `$params[] = $team` |
| Resolution Status | `dummy_5 = ?` | `$params[] = $resolution_status` |

### Filter Value Handling:

| **Condition** | **Action** |
|--------------|------------|
| `null` value | Filter not applied |
| Empty string `''` | Filter not applied (for date fields) |
| `'all'` value | Filter not applied (for dropdown fields) |
| Valid value | Filter applied with SQL condition |

---

## 6. Implementation Summary

### Total Components Updated:
- **23 Database Methods** in DatabaseInteraction.php
- **3 JavaScript Functions** in dashboard.php
- **9 Event Listeners** (6 new + 3 modified) in dashboard.php
- **7 API Endpoints** in data.php
- **6 New Filter Parameters** with complete mapping

### Filter System Capabilities:
- **10 Total Filters** (5 existing + 5 new enhanced filters)
- **Real-time AJAX Updates** across all dashboard components
- **Backward Compatibility** maintained for existing functionality
- **Extensible Framework** for future filter additions

### Data Flow Architecture:
```
Frontend Controls → Event Listeners → JavaScript Functions → 
AJAX Requests → data.php Endpoints → DatabaseInteraction Methods → 
SQL Queries with Filters → Filtered Results → Updated UI Components
```

This comprehensive filter system ensures that all dashboard components respond consistently to user filter selections, providing a unified and responsive user experience across the entire feedback analysis dashboard.
