# Bidirectional Cascading Filter System Documentation

## Overview

The bidirectional cascading filter system provides real-time validation and dynamic option updates for dashboard filters. It ensures users never encounter empty result sets by showing only filter options that have corresponding data when combined with other active filters.

## Architecture

### Core Components

1. **FilterValidation.php** - Backend validation service
2. **FilterLogger.php** - Comprehensive logging system
3. **filter_options.php** - API endpoints for filter operations
4. **filter-coordinator.js** - Frontend JavaScript coordination
5. **filter_monitor.php** - Monitoring dashboard

### Database Integration

- **DatabaseInteraction.php** - Enhanced with filter validation methods
- **data.php** - Extended with new filter endpoints
- **Composite indexes** - Optimized for filter combinations

## Features

### ✅ Bidirectional Filter Dependencies
- When ANY filter changes, ALL other filters update their available options
- Real-time validation prevents invalid filter combinations
- Maintains "All" options while filtering unavailable specific values

### ✅ Performance Optimization
- **Caching System**: 5-minute TTL for frequently accessed combinations
- **Debounced Updates**: Prevents excessive API calls
- **Batch Operations**: Single API calls for multiple filter updates
- **Composite Indexes**: Optimized database queries

### ✅ Comprehensive Logging
- **Operation Logs**: All filter state changes with timestamps
- **Performance Metrics**: Execution times and memory usage
- **Error Tracking**: Categorized error types with context
- **Debug Information**: SQL queries and filter combinations

### ✅ User Experience
- **Loading Indicators**: Visual feedback during filter updates
- **Error Messages**: Clear warnings for invalid combinations
- **Visual Indicators**: Shows when filters are actively constraining results
- **Auto-refresh**: Real-time updates without page reloads

## Filter Mapping

| Frontend Filter | Database Field | Description |
|----------------|----------------|-------------|
| Domain Category | domain_category | Hierarchical categories |
| Data ID | data_id | Unique dataset identifiers |
| Sentiment | sentiment | Positive/Neutral/Negative |
| Product Type | partner | Partner information |
| Channel Type | lob | Line of Business |
| Teams | dummy_1 | Team data |
| Resolution Status | dummy_5 | Resolution status data |
| Date Range | feedback_submit_date | Date filtering |

## API Endpoints

### filter_options.php

#### Get Filter Options
```
GET filter_options.php?action=get_options&filter_type=sentiment&data_id=123
```

#### Validate Filter Combination
```
GET filter_options.php?action=validate_combination&sentiment=Positive&data_id=123
```

#### Batch Filter Options
```
GET filter_options.php?action=batch_options&filter_types=sentiment,domain_category
```

#### Update Filter
```
GET filter_options.php?action=update_filter&updated_filter=sentiment&updated_value=Positive
```

### data.php Extensions

#### Validate Filter Combination
```
GET data.php?type=validate-filter-combination&sentiment=Positive
```

#### Dynamic Filter Options
```
GET data.php?type=dynamic-filter-options&filter_type=sentiment
```

## JavaScript Integration

### FilterCoordinator Class

```javascript
// Initialize
window.filterCoordinator = new FilterCoordinator();

// Set filter programmatically
filterCoordinator.setFilter('sentiment', 'Positive');

// Get current state
const filters = filterCoordinator.getCurrentFilters();

// Clear all filters
filterCoordinator.clearAllFilters();
```

### Event Handling

The system automatically handles:
- Filter dropdown changes
- AJAX requests for option updates
- Loading state management
- Error handling and user feedback

## Database Methods

### FilterValidation Class

```php
$validator = new FilterValidation();

// Validate combination
$hasData = $validator->validateFilterCombination($userId, $filters);

// Get available options
$options = $validator->getAvailableFilterOptions($userId, 'sentiment', $currentFilters);

// Batch operations
$batchOptions = $validator->getBatchFilterOptions($userId, $filterTypes, $currentFilters);
```

### DatabaseInteraction Extensions

```php
$db = new DatabaseInteraction();

// Validation
$isValid = $db->validateFilterCombination($userId, $filters);

// Get distinct values
$partners = $db->getDistinctPartners($userId);
$lobs = $db->getDistinctLobs($userId);
$teams = $db->getDistinctTeams($userId);
$statuses = $db->getDistinctResolutionStatuses($userId);
$dateRange = $db->getDateRange($userId);
```

## Monitoring and Debugging

### Filter Monitor Dashboard

Access: `filter_monitor.php`

Features:
- Real-time performance statistics
- Error tracking and categorization
- Recent operations log
- System information
- Maintenance actions

### Log Files

Located in `/logs/` directory:
- `filter_operations.log` - All filter operations
- `filter_performance.log` - Performance metrics
- `filter_errors.log` - Error tracking
- `filter_api.log` - API request logs

### Performance Metrics

The system tracks:
- Total requests per time period
- Average response times
- Slowest operations
- Memory usage
- Cache hit rates

## Testing

### Automated Tests

Run: `test/test_filter_validation.php`

Tests include:
- Basic filter validation
- Filter options retrieval
- Cascading dependencies
- Batch operations
- Performance benchmarks
- Cache functionality

### Manual Testing

1. **Filter Interactions**: Change filters and verify options update
2. **Invalid Combinations**: Ensure warnings appear for empty results
3. **Performance**: Monitor response times in browser dev tools
4. **Error Handling**: Test with invalid parameters

## Configuration

### Cache Settings

```php
// In FilterValidation.php
private $cache_ttl = 300; // 5 minutes
```

### Log Retention

```php
// In FilterLogger.php
$logger->cleanOldLogs(30); // Keep 30 days
```

### Performance Thresholds

```javascript
// In filter-coordinator.js
if (execution_time < 1000) { // 1 second threshold
    // Performance acceptable
}
```

## Troubleshooting

### Common Issues

1. **Slow Performance**
   - Check database indexes
   - Monitor cache hit rates
   - Review query complexity

2. **Empty Filter Options**
   - Verify data exists in database
   - Check filter mapping configuration
   - Review SQL query conditions

3. **JavaScript Errors**
   - Ensure filter-coordinator.js is loaded
   - Check browser console for errors
   - Verify DOM element IDs match

### Debug Mode

Enable detailed logging:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## Maintenance

### Regular Tasks

1. **Monitor Performance**: Check filter_monitor.php weekly
2. **Clean Logs**: Run monthly to prevent disk space issues
3. **Clear Cache**: Clear during deployments
4. **Review Errors**: Address recurring error patterns

### Database Maintenance

```sql
-- Check index usage
SHOW INDEX FROM analyzed_comments;

-- Analyze table performance
ANALYZE TABLE analyzed_comments;

-- Check for missing indexes
EXPLAIN SELECT * FROM analyzed_comments WHERE user_id = 1 AND sentiment = 'Positive';
```

## Future Enhancements

### Planned Features

1. **Advanced Caching**: Redis integration for distributed caching
2. **Real-time Updates**: WebSocket support for live filter updates
3. **Analytics**: User behavior tracking and filter usage analytics
4. **A/B Testing**: Filter interface optimization testing

### Scalability Considerations

1. **Database Sharding**: For large datasets
2. **CDN Integration**: For static filter assets
3. **Load Balancing**: For high-traffic scenarios
4. **Microservices**: Separate filter service architecture

## Support

For issues or questions:
1. Check the monitoring dashboard
2. Review log files for errors
3. Run automated tests
4. Consult this documentation

## Version History

- **v1.0**: Initial implementation with basic cascading filters
- **v1.1**: Added comprehensive logging and monitoring
- **v1.2**: Performance optimizations and caching
- **v1.3**: Enhanced error handling and user feedback
