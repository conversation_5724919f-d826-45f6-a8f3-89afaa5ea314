<?php

class DatabaseInteraction {
    private $conn;

    public function connect() {
        $this->conn = null;

         // $host = $_ENV['DB_HOST'];
         // $dbname = $_ENV['DB_NAME'];
         // $username = $_ENV['DB_USER'];
         // $password = $_ENV['DB_PASS'];

		// For your alternate environment, you might also include:
	       $host = $_ENV['DB_HOST_ALT'];
           $dbname = $_ENV['DB_NAME_ALT'];
           $username = $_ENV['DB_USER_ALT'];
           $password = $_ENV['DB_PASS_ALT'];


        try {
            $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8";
            $this->conn = new PDO($dsn, $username, $password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $exception) {
            throw new Exception("Database connection error: " . $exception->getMessage());
        }

        return $this->conn;
		}

/* class DatabaseInteraction {
    private $host = 'localhost';
    private $db_name = 'feedback_final_10';
    private $username = 'mk_hr_india';
    private $password = 'mkindiaggn';
    private $conn;

    // private $host = 'pub-gos-labs-project-wave-2.c1rgayqealwe.ap-south-1.rds.amazonaws.com';
    // private $db_name = 'goslabsprojectwave2';
    // private $username = 'deploy';
    // private $password = 'fNas2{7T8oBj';
    // private $conn;

	    public function connect() {
        $this->conn = null;

        try {
            $this->conn = new PDO("mysql:host=" . $this->host . ";dbname=" . $this->db_name, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            throw new Exception("Database connection error: " . $exception->getMessage());
        }

        return $this->conn;
    } */

    public function createUser($username, $passwordHash) {
        $query = "INSERT INTO user_profiles (username, password_hash) VALUES (:username, :password_hash)";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':password_hash', $passwordHash);
        return $stmt->execute();
    }

    public function getUser($username) {
        $query = "SELECT * FROM user_profiles WHERE username = :username";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function insertFeedbackData($data_id, $pid, $feedback_data, $csat, $nps, $user_id, $domain_category = null, $resolution_comment = null, $internal_scores = null, $feedback_submit_date = null, $feedback_month = null, $feedback_time = null, $lob = null, $vendor = null, $location = null, $partner = null, $dummy_1 = null, $dummy_2 = null, $dummy_3 = null, $dummy_4 = null, $dummy_5 = null) {
        // Insert into feedback_data table with all fields
        $query = "INSERT INTO feedback_data (
            data_id, pid, feedback_data, csat, nps, user_id, domain_category,
            resolution_comment, internal_scores, feedback_submit_date, feedback_month,
            feedback_time, lob, vendor, location, partner,
            dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
        ) VALUES (
            :data_id, :pid, :feedback_data, :csat, :nps, :user_id, :domain_category,
            :resolution_comment, :internal_scores, :feedback_submit_date, :feedback_month,
            :feedback_time, :lob, :vendor, :location, :partner,
            :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
        )";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':pid', $pid);
        $stmt->bindParam(':feedback_data', $feedback_data);
        $stmt->bindParam(':csat', $csat);
        $stmt->bindParam(':nps', $nps);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':domain_category', $domain_category);
        $stmt->bindParam(':resolution_comment', $resolution_comment);
        $stmt->bindParam(':internal_scores', $internal_scores);
        $stmt->bindParam(':feedback_submit_date', $feedback_submit_date);
        $stmt->bindParam(':feedback_month', $feedback_month);
        $stmt->bindParam(':feedback_time', $feedback_time);
        $stmt->bindParam(':lob', $lob);
        $stmt->bindParam(':vendor', $vendor);
        $stmt->bindParam(':location', $location);
        $stmt->bindParam(':partner', $partner);
        $stmt->bindParam(':dummy_1', $dummy_1);
        $stmt->bindParam(':dummy_2', $dummy_2);
        $stmt->bindParam(':dummy_3', $dummy_3);
        $stmt->bindParam(':dummy_4', $dummy_4);
        $stmt->bindParam(':dummy_5', $dummy_5);

        // Domain category is now saved directly to the feedback_data table

        try {
            $stmt->execute();
            return true; // Indicate success
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log("Database Error: " . $e->getMessage());
            return false; // Indicate failure
        }
    }

    public function getFeedbackDataByDataId($data_id) {
        $query = "SELECT * FROM feedback_data WHERE data_id = :data_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function insertAnalyzedComment($comment, $main_driver, $sub_driver, $sentiment, $user_id, $data_id, $csat, $nps, $pid, $painpointscustomerfrustrations, $detailedexplanationofthecomment, $suggestionsforimprovement, $verbatim, $domain_category) {
        // Get additional fields from comment_queue
        $query_queue = "SELECT resolution_comment, internal_scores, feedback_submit_date, feedback_month, feedback_time, lob, vendor, location, partner, dummy_1, dummy_2, dummy_3, dummy_4, dummy_5 FROM comment_queue WHERE comment = :comment AND data_id = :data_id LIMIT 1";
        $stmt_queue = $this->conn->prepare($query_queue);
        $stmt_queue->bindParam(':comment', $comment);
        $stmt_queue->bindParam(':data_id', $data_id);
        $stmt_queue->execute();
        $queue_row = $stmt_queue->fetch(PDO::FETCH_ASSOC);

        // Extract additional fields
        $resolution_comment = isset($queue_row['resolution_comment']) ? $queue_row['resolution_comment'] : null;
        $internal_scores = isset($queue_row['internal_scores']) ? $queue_row['internal_scores'] : null;
        $feedback_submit_date = isset($queue_row['feedback_submit_date']) ? $queue_row['feedback_submit_date'] : null;
        $feedback_month = isset($queue_row['feedback_month']) ? $queue_row['feedback_month'] : null;
        $feedback_time = isset($queue_row['feedback_time']) ? $queue_row['feedback_time'] : null;
        $lob = isset($queue_row['lob']) ? $queue_row['lob'] : null;
        $vendor = isset($queue_row['vendor']) ? $queue_row['vendor'] : null;
        $location = isset($queue_row['location']) ? $queue_row['location'] : null;
        $partner = isset($queue_row['partner']) ? $queue_row['partner'] : null;
        $dummy_1 = isset($queue_row['dummy_1']) ? $queue_row['dummy_1'] : null;
        $dummy_2 = isset($queue_row['dummy_2']) ? $queue_row['dummy_2'] : null;
        $dummy_3 = isset($queue_row['dummy_3']) ? $queue_row['dummy_3'] : null;
        $dummy_4 = isset($queue_row['dummy_4']) ? $queue_row['dummy_4'] : null;
        $dummy_5 = isset($queue_row['dummy_5']) ? $queue_row['dummy_5'] : null;

        // Insert into analyzed_comments with all fields
        $query = "INSERT INTO analyzed_comments (
            comment, main_driver, sub_driver, sentiment, user_id, data_id, csat, nps, pid,
            painpointscustomerfrustrations, detailedexplanationofthecomment, suggestionsforimprovement, verbitm, domain_category,
            resolution_comment, internal_scores, feedback_submit_date, feedback_month, feedback_time,
            lob, vendor, location, partner, dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
        ) VALUES (
            :comment, :main_driver, :sub_driver, :sentiment, :user_id, :data_id, :csat, :nps, :pid,
            :painpointscustomerfrustrations, :detailedexplanationofthecomment, :suggestionsforimprovement, :verbitm, :domain_category,
            :resolution_comment, :internal_scores, :feedback_submit_date, :feedback_month, :feedback_time,
            :lob, :vendor, :location, :partner, :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
        )";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':comment', $comment);
        $stmt->bindParam(':main_driver', $main_driver);
        $stmt->bindParam(':sub_driver', $sub_driver);
        $stmt->bindParam(':sentiment', $sentiment);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->bindParam(':csat', $csat);
        $stmt->bindParam(':nps', $nps);
        $stmt->bindParam(':pid', $pid);
        $stmt->bindParam(':painpointscustomerfrustrations', $painpointscustomerfrustrations);
        $stmt->bindParam(':detailedexplanationofthecomment', $detailedexplanationofthecomment);
        $stmt->bindParam(':suggestionsforimprovement', $suggestionsforimprovement);
        $stmt->bindParam(':verbitm', $verbatim);
        $stmt->bindParam(':domain_category', $domain_category);
        $stmt->bindParam(':resolution_comment', $resolution_comment);
        $stmt->bindParam(':internal_scores', $internal_scores);
        $stmt->bindParam(':feedback_submit_date', $feedback_submit_date);
        $stmt->bindParam(':feedback_month', $feedback_month);
        $stmt->bindParam(':feedback_time', $feedback_time);
        $stmt->bindParam(':lob', $lob);
        $stmt->bindParam(':vendor', $vendor);
        $stmt->bindParam(':location', $location);
        $stmt->bindParam(':partner', $partner);
        $stmt->bindParam(':dummy_1', $dummy_1);
        $stmt->bindParam(':dummy_2', $dummy_2);
        $stmt->bindParam(':dummy_3', $dummy_3);
        $stmt->bindParam(':dummy_4', $dummy_4);
        $stmt->bindParam(':dummy_5', $dummy_5);

        return $stmt->execute();
    }

    public function getAnalyzedComments($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
        $query = "SELECT * FROM analyzed_comments";
        $params = [];
        $conditions = [];

        // Dynamically add conditions
        if ($data_id !== null) {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($user_id !== null) {
            $conditions[] = "user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($sentiment !== null) {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null) {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        // If there are conditions, append them to the query
        if (count($conditions) > 0) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        // Prepare and execute the statement
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDataIdsByUserId($user_id) {
        $query = "SELECT DISTINCT data_id FROM feedback_data WHERE user_id = :user_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
	 public function getUniqueDomainCategories($user_id) {
        $query = "SELECT DISTINCT domain_category FROM analyzed_comments WHERE user_id = :user_id AND domain_category IS NOT NULL";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
    }
    public function getAllDomainCategories() {
        return [
            'Collections',
            'Customer Service',
            'Finance & Banking',
            'Retail & E-commerce',
            'Sales & Marketing',
            'Technical Support'
        ];
    }

    public function getMainDriversByDomainCategory($domain_category) {
        $drivers = [
            'Collections' => [
                'Billing & Payment Issues',
                'Customer Service & Resolution Issues',
                'Customer Support & Service',
                'Policy & Procedures',
                'Tools & Technology',
                'Transfer & Process Issues'
            ],
            'Customer Service' => [
                'Agent Behavior',
                'Call Quality Issues',
                'Communication Clarity',
                'Customer Driven',
                'First Call Resolution',
                'Knowledge Management',
                'Policy & Procedures',
                'Resolution Efficiency',
                'Tools & Technology'
            ],
            'Finance & Banking' => [
                'Account Access Issues',
                'Account Application',
		    'Agent Driven',
                'Billing & Payment Issues',
		'Customer Driven',
                'Customer Service & Resolution Issues',
                'Dispute Handling',
                'Loan & Credit Issues',
                'Policy & Procedures',
                'Refund Delays',
                'Refund Policies & Eligibility',
                'Tools & Technology',
                'Transaction Errors',
                'Verification Issues'
            ],
            'Retail & E-commerce' => [
                'Customer Service',
                'Delivery Experience',
                'Order Fulfillment',
                'Product Quality',
                'Returns & Refunds',
                'Website Experience'
            ],
            'Sales & Marketing' => [
                'Brand Representation',
                'Customer Service & Resolution Issues',
                'Customer Support & Service',
                'Follow-up Communication',
                'Lack of Clear Communication',
                'Lead Handling',
                'Marketing',
                'Perceived Company Integrity',
                'Product Knowledge Gaps',
                'Promotions & Discounts'
            ],
            'Technical Support' => [
                'Hardware Issues',
                'Self-Service Portal Issues',
                'Software Problems',
                'System Downtime',
                'Tool Integration',
                'User Accessibility'
            ]
        ];


        return isset($drivers[$domain_category]) ? $drivers[$domain_category] : [];
    }

    public function saveDataMainDrivers($data_id, $main_drivers) {
        if (empty($main_drivers)) {
            return true;
        }

        try {
            // First, delete any existing main drivers for this data_id
            $delete_query = "DELETE FROM data_main_drivers WHERE data_id = :data_id";
            $delete_stmt = $this->conn->prepare($delete_query);
            $delete_stmt->bindParam(':data_id', $data_id);
            $delete_stmt->execute();

            // Now insert the new main drivers
            $insert_query = "INSERT INTO data_main_drivers (data_id, main_driver) VALUES (:data_id, :main_driver)";
            $insert_stmt = $this->conn->prepare($insert_query);

            foreach ($main_drivers as $driver) {
                $insert_stmt->bindParam(':data_id', $data_id);
                $insert_stmt->bindParam(':main_driver', $driver);
                $insert_stmt->execute();
            }

            return true;
        } catch (PDOException $e) {
            error_log("Error saving main drivers: " . $e->getMessage());
            return false;
        }
    }

    public function getDataMainDrivers($data_id) {
        $query = "SELECT main_driver FROM data_main_drivers WHERE data_id = :data_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':data_id', $data_id);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
    }
	public function getDataIdsByDomain($user_id, $domain_category) {
    $query = "SELECT DISTINCT data_id FROM analyzed_comments
              WHERE user_id = :user_id AND domain_category = :domain_category";
    $stmt = $this->conn->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':domain_category', $domain_category, PDO::PARAM_STR);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_COLUMN, 0);
	}

	public function getDomainsByDataId($user_id, $data_id, $current_domain = null) {
    if ($data_id === 'all') {
        return $this->getUniqueDomainCategories($user_id);
    }

    $query = "SELECT DISTINCT domain_category FROM analyzed_comments
              WHERE user_id = :user_id AND data_id = :data_id
              AND domain_category IS NOT NULL AND domain_category != ''";

    // If we have a current domain selection, make sure it's included in results if valid
    if ($current_domain !== null) {
        $check_query = "SELECT COUNT(*) as count FROM analyzed_comments
                       WHERE user_id = :user_id AND data_id = :data_id
                       AND domain_category = :domain_category";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $check_stmt->bindParam(':data_id', $data_id, PDO::PARAM_STR);
        $check_stmt->bindParam(':domain_category', $current_domain, PDO::PARAM_STR);
        $check_stmt->execute();
        $result = $check_stmt->fetch(PDO::FETCH_ASSOC);

        // If current domain exists for this data_id, make sure it's included
        if ($result['count'] > 0) {
            $query .= " OR (user_id = :user_id AND domain_category = :domain_category)";
        }
    }

    $query .= " ORDER BY domain_category ASC";
    $stmt = $this->conn->prepare($query);
    $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
    $stmt->bindParam(':data_id', $data_id, PDO::PARAM_STR);

    if ($current_domain !== null) {
        $stmt->bindParam(':domain_category', $current_domain, PDO::PARAM_STR);
    }

    $stmt->execute();
    return array_map(function($row) {
        return html_entity_decode($row, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }, $stmt->fetchAll(PDO::FETCH_COLUMN, 0));
}

    public function getUploadedDataIdsByUser($user_id) {
        $conn = $this->connect();
        $stmt = $conn->prepare("
            SELECT data_id, MAX(created_at) as uploaded_at
            FROM feedback_data
            WHERE user_id = :user_id
            GROUP BY data_id
            ORDER BY uploaded_at DESC
        ");
        $stmt->execute(['user_id' => $user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    //filter is missing domain_category
	public function isCommentAnalyzed($comment, $data_id, $pid = null, $sentiment = null, $domain_category = null) {
    $query = "SELECT COUNT(*) AS count
              FROM analyzed_comments
              WHERE comment = :comment
                AND data_id = :data_id";

    // Only add pid to the query if it's provided
    if ($pid !== null) {
        $query .= " AND pid = :pid";
    }

    if ($sentiment && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
    }

    if ($domain_category && $domain_category !== 'all') {
        $query .= " AND domain_category = :domain_category";
    }

    $stmt = $this->conn->prepare($query);
    $stmt->bindParam(':comment', $comment);
    $stmt->bindParam(':data_id', $data_id);

    // Only bind pid if it's provided
    if ($pid !== null) {
        $stmt->bindParam(':pid', $pid);
    }

    if ($sentiment && $sentiment !== 'all') {
        $stmt->bindParam(':sentiment', $sentiment);
    }

    if ($domain_category && $domain_category !== 'all') {
        $stmt->bindParam(':domain_category', $domain_category);
    }

    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return $result['count'] > 0;
}
	public function getTotalComments($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                 $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                 $team = null, $resolution_status = null) {
    $query = "SELECT COUNT(*) AS total FROM analyzed_comments";
    $params = [];
    $conditions = [];

    if ($data_id !== null) {
        $conditions[] = "data_id = :data_id";
        $params[':data_id'] = $data_id;
    }
    if ($user_id !== null) {
        $conditions[] = "user_id = :user_id";
        $params[':user_id'] = $user_id;
    }
    if ($sentiment !== null && $sentiment !== 'all') {
        $conditions[] = "sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }
    if ($domain_category !== null) {
        $conditions[] = "domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // New filter conditions
    if ($start_date !== null) {
        $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }
    if ($end_date !== null) {
        $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }
    if ($product_type !== null) {
        $conditions[] = "partner = :product_type";
        $params[':product_type'] = $product_type;
    }
    if ($channel_type !== null) {
        $conditions[] = "lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }
    if ($team !== null) {
        $conditions[] = "dummy_1 = :team";
        $params[':team'] = $team;
    }
    if ($resolution_status !== null) {
        $conditions[] = "dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    if (count($conditions) > 0) {
        $query .= " WHERE " . implode(" AND ", $conditions);
    }
    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    return $result['total'];
}


    public function getMainDriversCount($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                       $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                       $team = null, $resolution_status = null) {
        $query = "SELECT main_driver, COUNT(*) AS count FROM analyzed_comments";
        $params = [];
        $conditions = [];

        if ($data_id) {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($user_id) {
            $conditions[] = "user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($sentiment && $sentiment !== 'all') {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category && $domain_category !== 'all') {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $conditions[] = "partner = :product_type";
            $params[':product_type'] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $conditions[] = "lob = :channel_type";
            $params[':channel_type'] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $conditions[] = "dummy_1 = :team";
            $params[':team'] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $conditions[] = "dummy_5 = :resolution_status";
            $params[':resolution_status'] = $resolution_status;
        }

        if (count($conditions) > 0) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " GROUP BY main_driver";
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $main_drivers_count = [];
        foreach ($results as $result) {
            $main_drivers_count[$result['main_driver']] = $result['count'];
        }
        return $main_drivers_count;
    }

    public function getSentimentsCount($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                     $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                     $team = null, $resolution_status = null) {
        $query = "SELECT sentiment, COUNT(*) AS count FROM analyzed_comments";
        $params = [];
        $conditions = [];

        if ($data_id) {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($user_id) {
            $conditions[] = "user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($sentiment && $sentiment !== 'all') {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category && $domain_category !== 'all') {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        // New filter conditions
        if ($start_date !== null) {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $start_date;
        }
        if ($end_date !== null) {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $end_date;
        }
        if ($product_type !== null) {
            $conditions[] = "partner = :product_type";
            $params[':product_type'] = $product_type;
        }
        if ($channel_type !== null) {
            $conditions[] = "lob = :channel_type";
            $params[':channel_type'] = $channel_type;
        }
        if ($team !== null) {
            $conditions[] = "dummy_1 = :team";
            $params[':team'] = $team;
        }
        if ($resolution_status !== null) {
            $conditions[] = "dummy_5 = :resolution_status";
            $params[':resolution_status'] = $resolution_status;
        }

        if (count($conditions) > 0) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " GROUP BY sentiment";
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $sentiments_count = [];
        foreach ($results as $result) {
            $sentiments_count[$result['sentiment']] = $result['count'];
        }

        return $sentiments_count;
    }


    public function getSubDriversCount($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
    $query = "SELECT main_driver, sub_driver FROM analyzed_comments";
    $conditions = [];
    $params = [];

    if ($data_id !== null) {
        $conditions[] = "data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($user_id !== null) {
        $conditions[] = "user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $conditions[] = "sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $conditions[] = "domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    if (!empty($conditions)) {
        $query .= " WHERE " . implode(" AND ", $conditions);
    }

    $query .= " GROUP BY main_driver, sub_driver";
    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $sub_drivers_count = [];
    foreach ($results as $result) {
        if (!isset($sub_drivers_count[$result['main_driver']])) {
            $sub_drivers_count[$result['main_driver']] = [];
        }
        $sub_drivers_count[$result['main_driver']][] = $result['sub_driver'];
    }

    return $sub_drivers_count;
}

    public function getSentimentsAcrossMainDriversCount($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                                       $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                                       $team = null, $resolution_status = null) {
        $query = "SELECT main_driver, sentiment, COUNT(*) AS count FROM analyzed_comments";
        $params = [];
        $conditions = [];

        if ($data_id) {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($user_id) {
            $conditions[] = "user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($sentiment && $sentiment !== 'all') {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category && $domain_category !== 'all') {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $conditions[] = "partner = :product_type";
            $params[':product_type'] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $conditions[] = "lob = :channel_type";
            $params[':channel_type'] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $conditions[] = "dummy_1 = :team";
            $params[':team'] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $conditions[] = "dummy_5 = :resolution_status";
            $params[':resolution_status'] = $resolution_status;
        }

        if (count($conditions) > 0) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $query .= " GROUP BY main_driver, sentiment";
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $sentiments_main_drivers_count = [];
        foreach ($results as $result) {
            if (!isset($sentiments_main_drivers_count[$result['main_driver']])) {
                $sentiments_main_drivers_count[$result['main_driver']] = [];
            }
            $sentiments_main_drivers_count[$result['main_driver']][$result['sentiment']] = $result['count'];
        }

        return $sentiments_main_drivers_count;
    }


public function getSubDriversContribution($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                         $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                         $team = null, $resolution_status = null) {
    $query = "SELECT
        main_driver,
        sub_driver,
        COUNT(*) AS total_count,
        SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_count,
        SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_count,
        SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_count,
        AVG(csat) as avg_csat,
        AVG(nps) as avg_nps
    FROM analyzed_comments";

    $where = [];
    $params = [];

    if ($data_id) {
        $where[] = "data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($user_id) {
        $where[] = "user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($sentiment && $sentiment !== 'all') {
        $where[] = "sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null && $domain_category !== 'all') {
        $where[] = "domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $where[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $where[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $where[] = "partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $where[] = "lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $where[] = "dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $where[] = "dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    if (!empty($where)) {
        $query .= " WHERE " . implode(" AND ", $where);
    }

    $query .= " GROUP BY main_driver, sub_driver";
    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $sub_drivers_contribution = [];
    foreach ($results as $result) {
        // Calculate percentages with 1 decimal place
        $total = $result['total_count'];
        $positive_percentage = $total > 0 ? round(($result['positive_count'] / $total) * 100, 1) : 0;
        $neutral_percentage = $total > 0 ? round(($result['neutral_count'] / $total) * 100, 1) : 0;
        $negative_percentage = $total > 0 ? round(($result['negative_count'] / $total) * 100, 1) : 0;

        // Format CSAT and NPS to always have 2 decimal places
        $avg_csat = is_null($result['avg_csat']) ? 0 : round($result['avg_csat'], 2);
        $avg_nps = is_null($result['avg_nps']) ? 0 : round($result['avg_nps'], 2);

        $sub_drivers_contribution[$result['main_driver']][] = [
            'sub_driver' => $result['sub_driver'],
            'total_count' => $total,
            'positive_count' => $result['positive_count'],
            'neutral_count' => $result['neutral_count'],
            'negative_count' => $result['negative_count'],
            'positive_percentage' => $positive_percentage,
            'neutral_percentage' => $neutral_percentage,
            'negative_percentage' => $negative_percentage,
            'avg_csat' => $avg_csat,
            'avg_nps' => $avg_nps
        ];
    }

    return $sub_drivers_contribution;
}

	public function getSubDriversByMainDriver($data_id = null, $user_id = null, $main_driver = null, $sentiment = null, $domain_category = null) {
    $query = "SELECT sub_driver FROM analyzed_comments";
    $where = [];
    $params = [];

    if ($data_id) {
        $where[] = "data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($user_id) {
        $where[] = "user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($main_driver) {
        $where[] = "main_driver = :main_driver";
        $params[':main_driver'] = $main_driver;
    }

    if ($sentiment && $sentiment !== 'all') {
        $where[] = "sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $where[] = "domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    if (!empty($where)) {
        $query .= " WHERE " . implode(" AND ", $where);
    }

    $query .= " GROUP BY sub_driver";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $sub_drivers_by_main_driver = [];
    foreach ($results as $result) {
        $sub_drivers_by_main_driver[] = $result['sub_driver'];
    }

    return $sub_drivers_by_main_driver;
}

public function getSubDriversTableData($data_id = null, $user_id = null, $main_driver = null, $sub_driver = null, $sentiment = null, $domain_category = null) {
    $query = "SELECT main_driver, sub_driver, sentiment, COUNT(*) AS count FROM analyzed_comments";
    $where = [];
    $params = [];

    if ($data_id) {
        $where[] = "data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($user_id) {
        $where[] = "user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($main_driver) {
        $where[] = "main_driver = :main_driver";
        $params[':main_driver'] = $main_driver;
    }

    if ($sub_driver) {
        $where[] = "sub_driver = :sub_driver";
        $params[':sub_driver'] = $sub_driver;
    }

    if ($sentiment && $sentiment !== 'all') {
        $where[] = "sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $where[] = "domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    if (!empty($where)) {
        $query .= " WHERE " . implode(" AND ", $where);
    }

    $query .= " GROUP BY main_driver, sub_driver, sentiment";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}


    public function getWordCloudData($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                    $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                    $team = null, $resolution_status = null) {
        $query = "SELECT verbitm FROM analyzed_comments";
        $params = [];
        $conditions = [];

        if ($data_id) {
            $conditions[] = "data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($user_id) {
            $conditions[] = "user_id = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($sentiment && $sentiment !== 'all') {
            $conditions[] = "sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category && $domain_category !== 'all') {
            $conditions[] = "domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $conditions[] = "DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $conditions[] = "partner = :product_type";
            $params[':product_type'] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $conditions[] = "lob = :channel_type";
            $params[':channel_type'] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $conditions[] = "dummy_1 = :team";
            $params[':team'] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $conditions[] = "dummy_5 = :resolution_status";
            $params[':resolution_status'] = $resolution_status;
        }

        if (count($conditions) > 0) {
            $query .= " WHERE " . implode(" AND ", $conditions);
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $word_cloud_data = [];
        foreach ($results as $result) {
            if (!empty($result['verbitm'])) {
                $word_cloud_data[] = $result['verbitm'];
            }
        }
        return $word_cloud_data;
    }

	public function getTimeSeriesSentiments($data_id, $user_id, $sentiment, $domain_category = null, $days = 7, $view_type = 'daily',
                                        $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                        $team = null, $resolution_status = null) {
    // Default to 7 days if not specified
    $days = intval($days) > 0 ? intval($days) : 7;

    // Adjust the date format based on view type
    $dateFormat = "DATE(IFNULL(ac.feedback_submit_date, ac.created_at))";
    $groupBy = "DATE(IFNULL(ac.feedback_submit_date, ac.created_at))";
    $displayFormat = "";

    if ($view_type === 'monthly') {
        // For monthly view, group by year and month
        $dateFormat = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%Y-%m-01')";
        $groupBy = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%Y-%m')";
        $displayFormat = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%b %Y')";
    } else if ($view_type === 'yearly') {
        // For yearly view, group by year only
        $dateFormat = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%Y-01-01')";
        $groupBy = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%Y')";
        $displayFormat = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%Y')";
    } else {
        // For daily view, include display format
        $displayFormat = "DATE_FORMAT(IFNULL(ac.feedback_submit_date, ac.created_at), '%m/%d/%Y')";
    }

    // Build the query with the display format
    $query = "SELECT
                $dateFormat AS date,
                " . ($displayFormat ? "$displayFormat AS display_date," : "") . "
                SUM(CASE WHEN ac.sentiment = 'Positive' THEN 1 ELSE 0 END) AS Positive,
                SUM(CASE WHEN ac.sentiment = 'Neutral' THEN 1 ELSE 0 END) AS Neutral,
                SUM(CASE WHEN ac.sentiment = 'Negative' THEN 1 ELSE 0 END) AS Negative
            FROM
                analyzed_comments ac
            WHERE
                ac.user_id = :user_id
                AND (:data_id IS NULL OR ac.data_id = :data_id)
                AND (:sentiment IS NULL OR ac.sentiment = :sentiment)";

    // Only add date restriction if not 'all'
    if ($days !== 'all') {
        $query .= " AND DATE(IFNULL(ac.feedback_submit_date, ac.created_at)) >= DATE_SUB(CURDATE(), INTERVAL :days DAY)";
    }

    $params = [
        ':user_id' => $user_id,
        ':data_id' => $data_id,
        ':sentiment' => ($sentiment === 'all' || empty($sentiment)) ? null : $sentiment
    ];

    // Only add days parameter if not 'all'
    if ($days !== 'all') {
        $params[':days'] = $days;
    }

    if ($domain_category !== null && $domain_category !== 'all') {
        $query .= " AND ac.domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(ac.feedback_submit_date, ac.created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(ac.feedback_submit_date, ac.created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND ac.partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND ac.lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND ac.dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND ac.dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= "
            GROUP BY
                $groupBy
            HAVING
                SUM(CASE WHEN ac.sentiment IN ('Positive', 'Neutral', 'Negative') THEN 1 ELSE 0 END) > 0
            ORDER BY
                date";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Format the results
    $output = [];
    foreach ($results as $row) {
        $output[$row['date']] = [
            'date' => $row['date'],
            'display_date' => $row['display_date'] ?? $row['date'],
            'Positive' => (int)$row['Positive'],
            'Neutral' => (int)$row['Neutral'],
            'Negative' => (int)$row['Negative']
        ];
    }

    return $output;
}

public function getMainDriversSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                       $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                       $team = null, $resolution_status = null) {
    $query = "SELECT main_driver,
                     SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as Positive,
                     SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as Neutral,
                     SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as Negative
              FROM analyzed_comments
              WHERE user_id = :user_id";

    $params = [':user_id' => $user_id];

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY main_driver ORDER BY COUNT(*) DESC LIMIT 20";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $formatted = [];
    foreach ($results as $row) {
        $formatted[$row['main_driver']] = [
            'Positive' => (int)$row['Positive'],
            'Neutral' => (int)$row['Neutral'],
            'Negative' => (int)$row['Negative']
        ];
    }

    return $formatted;
}

     public function getSubDriversSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                           $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                           $team = null, $resolution_status = null) {
    $query = "SELECT sub_driver,
                     SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as Positive,
                     SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as Neutral,
                     SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as Negative
              FROM analyzed_comments
              WHERE user_id = :user_id";

    $params = [':user_id' => $user_id];

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver ORDER BY COUNT(*) DESC LIMIT 20";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);

    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $formatted = [];
    foreach ($results as $row) {
        $formatted[$row['sub_driver']] = [
            'Positive' => (int)$row['Positive'],
            'Neutral' => (int)$row['Neutral'],
            'Negative' => (int)$row['Negative']
        ];
    }

    return $formatted;
}


    public function getDetailedSentimentsData($user_id, $data_id = null, $sentiment = null, $domain_category = null,
                                             $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                             $team = null, $resolution_status = null) {
    $query = "SELECT main_driver AS driver,
                     COUNT(*) AS count,
                     SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) AS positive_count,
                     SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) AS neutral_count,
                     SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) AS negative_count
              FROM analyzed_comments
              WHERE user_id = :user_id";

    $params = [':user_id' => $user_id];

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY main_driver ORDER BY count DESC";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $detailed_data = [];

    foreach ($results as $row) {
        $total = $row['count'];
        $positive_percentage = $total ? round(($row['positive_count'] / $total) * 100, 2) : 0;
        $neutral_percentage = $total ? round(($row['neutral_count'] / $total) * 100, 2) : 0;
        $negative_percentage = $total ? round(($row['negative_count'] / $total) * 100, 2) : 0;

        // Calculate weighted actual points
        $actual_points = ($row['positive_count'] * 1) + ($row['neutral_count'] * 0.5) + ($row['negative_count'] * -1);

        // Calculate possible points (if all were positive)
        $possible_points = $total;

        // Calculate sentiment score as (Sum of Actual Points) / (Sum of Possible Points) * 100
        if ($possible_points > 0) {
            $sentiment_score = round(($actual_points / $possible_points) * 100, 2);
        } else {
            $sentiment_score = 0;
        }

        // Adjust thresholds for the percentage-based score
        if ($sentiment_score >= 25) {
            $sentiment_level = "🟢"; // Green for positive scores >= 25%
        } elseif ($sentiment_score >= 0) {
            $sentiment_level = "🟡"; // Yellow for positive scores between 0% and 25%
        } elseif ($sentiment_score >= -25) {
            $sentiment_level = "🟠"; // Orange for scores between -25% and 0%
        } else {
            $sentiment_level = "🔴"; // Red for negative scores < -25%
        }

        $detailed_data[] = [
            'driver' => $row['driver'],
            'count' => $total,
            'positive_count' => $row['positive_count'],
            'neutral_count' => $row['neutral_count'],
            'negative_count' => $row['negative_count'],
            'positive_percentage' => $positive_percentage,
            'neutral_percentage' => $neutral_percentage,
            'negative_percentage' => $negative_percentage,
            'sentiment_score' => $sentiment_score,
            'sentiment_level' => $sentiment_level
        ];
    }

    return $detailed_data;
}

    public function getTopPositiveSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                            $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                            $team = null, $resolution_status = null) {
    $query = "SELECT sub_driver, COUNT(*) as positive
              FROM analyzed_comments
              WHERE 1=1";

    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment === null || strtolower($sentiment) === 'positive') {
        $query .= " AND sentiment = 'Positive'";
    } elseif (strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver ORDER BY positive DESC LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getTopNegativeSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                        $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                        $team = null, $resolution_status = null) {
    $query = "SELECT sub_driver, COUNT(*) as negative
              FROM analyzed_comments
              WHERE 1=1";

    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment === null || strtolower($sentiment) === 'negative') {
        $query .= " AND sentiment = 'Negative'";
    } elseif (strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver ORDER BY negative DESC LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getTopCsatImpactSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                          $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                          $team = null, $resolution_status = null) {
    $query = "SELECT sub_driver, AVG(csat) as average_csat
              FROM analyzed_comments
              WHERE 1=1";

    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver
                HAVING COUNT(*) > 5
                ORDER BY average_csat DESC
                LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getTopNpsImpactSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                         $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                         $team = null, $resolution_status = null) {
    $query = "SELECT sub_driver, AVG(nps) as average_nps
              FROM analyzed_comments
              WHERE 1=1";

    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver
                HAVING COUNT(*) > 5
                ORDER BY average_nps DESC
                LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getTopNpsPromoterSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                           $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                           $team = null, $resolution_status = null) {
    // Get scale configuration for dynamic promoter threshold
    $scale_config = $this->getScaleConfiguration($data_id, $user_id);
    $promoter_threshold = $scale_config['nps_promoter_threshold'];

    $query = "SELECT sub_driver, COUNT(*) as promoter
              FROM analyzed_comments
              WHERE 1=1 AND nps >= :promoter_threshold";

    $params = [':promoter_threshold' => $promoter_threshold];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver
                ORDER BY promoter DESC
                LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

public function getTopNpsDetractorSubDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                            $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                            $team = null, $resolution_status = null) {
    // Get scale configuration for dynamic detractor threshold
    $scale_config = $this->getScaleConfiguration($data_id, $user_id);
    $detractor_threshold = $scale_config['nps_detractor_threshold'];

    $query = "SELECT sub_driver, COUNT(*) as detractor
              FROM analyzed_comments
              WHERE 1=1 AND nps <= :detractor_threshold";

    $params = [':detractor_threshold' => $detractor_threshold];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && strtolower($sentiment) !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY sub_driver
                ORDER BY detractor DESC
                LIMIT 5";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

 public function getFeedbackCommentsSummary($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                           $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                           $team = null, $resolution_status = null) {
    // Get the top contributor comments
    $topContributorData = $this->getTopContributorFeedbackComments($data_id, $user_id, $sentiment, $domain_category,
                                                                   $start_date, $end_date, $product_type, $channel_type,
                                                                   $team, $resolution_status);

    // Define keywords for emphasis
    $keywords = [
        'good', 'great', 'excellent', 'impressed', 'exceeding', 'outstanding', 'superb', 'pleasant', 'innovative', 'efficient', 'friendly', 'helpful', 'enthusiastic', 'satisfied', 'recommended', 'endorsements',
        'bad', 'poor', 'terrible', 'ineffective', 'difficult', 'unfriendly', 'frustrating', 'worse', 'unhelpful', 'expensive', 'lacking', 'problematic', 'unhappy', 'disappointed'
    ];

    return [
        'highlights' => $topContributorData['positive_comments'],
        'lowlights' => $topContributorData['negative_comments'],
        'keywords' => $keywords,
        'top_contributor' => $topContributorData['top_contributor'],
        'contribution_percentage' => $topContributorData['contribution_percentage']
    ];
}

/**
 * Get top positive and negative comments for the top contributing main_driver
 *
 * @param string|null $data_id
 * @param int|null $user_id
 * @param string|null $sentiment
 * @param string|null $domain_category
 * @return array
 */
public function getTopContributorFeedbackComments($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                               $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                               $team = null, $resolution_status = null) {
    // Step 1: Find the top contributing main_driver (excluding "NA" and "Other")
    $query = "SELECT main_driver, COUNT(*) AS count
              FROM analyzed_comments
              WHERE main_driver NOT IN ('NA', 'Other', '')";
    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $query .= " GROUP BY main_driver ORDER BY count DESC LIMIT 3";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Default values if no results
    $topContributor = "No Main Driver";
    $contributionPercentage = 0;
    $totalCount = 0;

    // Get the top contributor (first result)
    if (!empty($results)) {
        $topContributor = $results[0]['main_driver'];
        $topContributorCount = $results[0]['count'];

        // Get total count for percentage calculation
        $totalQuery = "SELECT COUNT(*) AS total FROM analyzed_comments WHERE 1=1";
        $totalParams = [];

        if ($user_id !== null) {
            $totalQuery .= " AND user_id = :user_id";
            $totalParams[':user_id'] = $user_id;
        }

        if ($data_id !== null) {
            $totalQuery .= " AND data_id = :data_id";
            $totalParams[':data_id'] = $data_id;
        }

        if ($sentiment !== null && $sentiment !== 'all') {
            $totalQuery .= " AND sentiment = :sentiment";
            $totalParams[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null) {
            $totalQuery .= " AND domain_category = :domain_category";
            $totalParams[':domain_category'] = $domain_category;
        }

        $totalStmt = $this->conn->prepare($totalQuery);
        $totalStmt->execute($totalParams);
        $totalResult = $totalStmt->fetch(PDO::FETCH_ASSOC);
        $totalCount = $totalResult['total'];

        // Calculate percentage
        $contributionPercentage = ($totalCount > 0) ? round(($topContributorCount / $totalCount) * 100, 1) : 0;
    } else {
        // If no main drivers found, return empty results
        return [
            'top_contributor' => $topContributor,
            'contribution_percentage' => $contributionPercentage,
            'positive_comments' => [],
            'negative_comments' => []
        ];
    }

    // Step 2: Get top 3 positive comments for this main_driver
    $positiveQuery = "SELECT detailedexplanationofthecomment
                     FROM analyzed_comments
                     WHERE main_driver = :main_driver
                     AND sentiment = 'Positive'";
    $positiveParams = [':main_driver' => $topContributor];

    if ($user_id !== null) {
        $positiveQuery .= " AND user_id = :user_id";
        $positiveParams[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $positiveQuery .= " AND data_id = :data_id";
        $positiveParams[':data_id'] = $data_id;
    }

    if ($domain_category !== null) {
        $positiveQuery .= " AND domain_category = :domain_category";
        $positiveParams[':domain_category'] = $domain_category;
    }

    // Add new filter parameters to positive query
    if ($start_date !== null && $start_date !== '') {
        $positiveQuery .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $positiveParams[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $positiveQuery .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $positiveParams[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $positiveQuery .= " AND partner = :product_type";
        $positiveParams[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $positiveQuery .= " AND lob = :channel_type";
        $positiveParams[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $positiveQuery .= " AND dummy_1 = :team";
        $positiveParams[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $positiveQuery .= " AND dummy_5 = :resolution_status";
        $positiveParams[':resolution_status'] = $resolution_status;
    }

    $positiveQuery .= " ORDER BY RAND() LIMIT 3";

    $positiveStmt = $this->conn->prepare($positiveQuery);
    $positiveStmt->execute($positiveParams);
    $positiveResults = $positiveStmt->fetchAll(PDO::FETCH_COLUMN);

    // Step 3: Get top 3 negative comments for this main_driver
    $negativeQuery = "SELECT detailedexplanationofthecomment
                     FROM analyzed_comments
                     WHERE main_driver = :main_driver
                     AND sentiment = 'Negative'";
    $negativeParams = [':main_driver' => $topContributor];

    if ($user_id !== null) {
        $negativeQuery .= " AND user_id = :user_id";
        $negativeParams[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $negativeQuery .= " AND data_id = :data_id";
        $negativeParams[':data_id'] = $data_id;
    }

    if ($domain_category !== null) {
        $negativeQuery .= " AND domain_category = :domain_category";
        $negativeParams[':domain_category'] = $domain_category;
    }

    // Add new filter parameters to negative query
    if ($start_date !== null && $start_date !== '') {
        $negativeQuery .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $negativeParams[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $negativeQuery .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $negativeParams[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $negativeQuery .= " AND partner = :product_type";
        $negativeParams[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $negativeQuery .= " AND lob = :channel_type";
        $negativeParams[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $negativeQuery .= " AND dummy_1 = :team";
        $negativeParams[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $negativeQuery .= " AND dummy_5 = :resolution_status";
        $negativeParams[':resolution_status'] = $resolution_status;
    }

    $negativeQuery .= " ORDER BY RAND() LIMIT 3";

    $negativeStmt = $this->conn->prepare($negativeQuery);
    $negativeStmt->execute($negativeParams);
    $negativeResults = $negativeStmt->fetchAll(PDO::FETCH_COLUMN);

    return [
        'top_contributor' => $topContributor,
        'contribution_percentage' => $contributionPercentage,
        'positive_comments' => $positiveResults,
        'negative_comments' => $negativeResults
    ];
}

    // Historical Sentiments (New Method)
	public function getHistoricalSentiments($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
    $query = "SELECT sentiment, COUNT(*) AS count FROM analyzed_comments WHERE IFNULL(feedback_submit_date, created_at) BETWEEN DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND CURDATE()";
    $params = [];

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    $query .= " GROUP BY sentiment";

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $historical_sentiments = [];
    foreach ($results as $result) {
        $historical_sentiments[$result['sentiment']] = (int)$result['count'];
    }

    return $historical_sentiments;
}
	public function getCsatImpact($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                                 $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                 $team = null, $resolution_status = null) {
    // Get scale configuration for dynamic baseline
    $scale_config = $this->getScaleConfiguration($data_id, $user_id);
    $csat_baseline = $scale_config['csat_baseline'];

    $query = "
        SELECT
            AVG(csat) AS average_csat,
            GROUP_CONCAT(DISTINCT sub_driver ORDER BY csat DESC SEPARATOR ', ') AS top_positive_drivers,
            GROUP_CONCAT(DISTINCT sub_driver ORDER BY csat ASC SEPARATOR ', ') AS top_negative_drivers
        FROM analyzed_comments
        WHERE csat IS NOT NULL";

    $params = [];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $average_csat = isset($result['average_csat']) ? (float)$result['average_csat'] : 0;
    $csat_impact = round($average_csat - $csat_baseline, 2); // Using dynamic baseline from scale configuration

    $positive_factors = !empty($result['top_positive_drivers']) ? explode(', ', $result['top_positive_drivers']) : [];
    $negative_factors = !empty($result['top_negative_drivers']) ? explode(', ', $result['top_negative_drivers']) : [];

    return [
        'csat_impact' => $csat_impact,
        'positive_factors' => array_slice($positive_factors, 0, 5),
        'negative_factors' => array_slice($negative_factors, 0, 5)
    ];
}

public function getNpsImpact($data_id = null, $user_id = null, $sentiment = null, $domain_category = null,
                            $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                            $team = null, $resolution_status = null) {
    // Get scale configuration for dynamic thresholds
    $scale_config = $this->getScaleConfiguration($data_id, $user_id);
    $promoter_threshold = $scale_config['nps_promoter_threshold'];
    $detractor_threshold = $scale_config['nps_detractor_threshold'];

    $query = "
        SELECT
            SUM(CASE WHEN nps >= :promoter_threshold THEN 1 ELSE 0 END) AS promoters,
            SUM(CASE WHEN nps <= :detractor_threshold THEN 1 ELSE 0 END) AS detractors,
            GROUP_CONCAT(DISTINCT sub_driver ORDER BY nps DESC SEPARATOR ', ') AS top_positive_drivers,
            GROUP_CONCAT(DISTINCT sub_driver ORDER BY nps ASC SEPARATOR ', ') AS top_negative_drivers
        FROM analyzed_comments
        WHERE nps IS NOT NULL";

    $params = [
        ':promoter_threshold' => $promoter_threshold,
        ':detractor_threshold' => $detractor_threshold
    ];

    if ($user_id !== null) {
        $query .= " AND user_id = :user_id";
        $params[':user_id'] = $user_id;
    }

    if ($data_id !== null) {
        $query .= " AND data_id = :data_id";
        $params[':data_id'] = $data_id;
    }

    if ($sentiment !== null && $sentiment !== 'all') {
        $query .= " AND sentiment = :sentiment";
        $params[':sentiment'] = $sentiment;
    }

    if ($domain_category !== null) {
        $query .= " AND domain_category = :domain_category";
        $params[':domain_category'] = $domain_category;
    }

    // Add new filter parameters
    if ($start_date !== null && $start_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
        $params[':start_date'] = $start_date;
    }

    if ($end_date !== null && $end_date !== '') {
        $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
        $params[':end_date'] = $end_date;
    }

    if ($product_type !== null && $product_type !== 'all') {
        $query .= " AND partner = :product_type";
        $params[':product_type'] = $product_type;
    }

    if ($channel_type !== null && $channel_type !== 'all') {
        $query .= " AND lob = :channel_type";
        $params[':channel_type'] = $channel_type;
    }

    if ($team !== null && $team !== 'all') {
        $query .= " AND dummy_1 = :team";
        $params[':team'] = $team;
    }

    if ($resolution_status !== null && $resolution_status !== 'all') {
        $query .= " AND dummy_5 = :resolution_status";
        $params[':resolution_status'] = $resolution_status;
    }

    $stmt = $this->conn->prepare($query);
    $stmt->execute($params);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $promoters = isset($result['promoters']) ? (int)$result['promoters'] : 0;
    $detractors = isset($result['detractors']) ? (int)$result['detractors'] : 0;

    $total_responses = $promoters + $detractors;
    $nps = $total_responses > 0 ? round(($promoters - $detractors) / $total_responses * 100, 2) : 0;

    $positive_factors = !empty($result['top_positive_drivers']) ? explode(', ', $result['top_positive_drivers']) : [];
    $negative_factors = !empty($result['top_negative_drivers']) ? explode(', ', $result['top_negative_drivers']) : [];

    return [
        'nps_impact' => $nps,
        'positive_factors' => array_slice($positive_factors, 0, 5),
        'negative_factors' => array_slice($negative_factors, 0, 5)
    ];
}

    // Queue Management Functions
    public function enqueueComment($comment, $data_id, $user_id, $csat, $nps, $pid) {
        // Log the enqueue attempt for debugging
        error_log("Attempting to enqueue comment for data_id: $data_id, user_id: $user_id");

        try {
            // Get the additional fields from the feedback_data table
            $query_feedback = "SELECT domain_category, resolution_comment, internal_scores, feedback_submit_date,
                              feedback_month, feedback_time, lob, vendor, location, partner,
                              dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
                              FROM feedback_data
                              WHERE data_id = :data_id AND feedback_data = :comment LIMIT 1";

            try {
                $stmt_feedback = $this->conn->prepare($query_feedback);
                if (!$stmt_feedback) {
                    error_log("ERROR: Failed to prepare feedback query: " . json_encode($this->conn->errorInfo()));
                    return false;
                }

                $stmt_feedback->bindParam(':data_id', $data_id);
                $stmt_feedback->bindParam(':comment', $comment);
                $stmt_feedback->execute();
                $feedback_row = $stmt_feedback->fetch(PDO::FETCH_ASSOC);

                // Log if we found the feedback data
                if (!$feedback_row) {
                    error_log("Warning: No feedback data found for data_id: $data_id and comment. Using null values for additional fields.");
                    // Try a more lenient query without the comment match
                    $query_feedback_alt = "SELECT domain_category, resolution_comment, internal_scores, feedback_submit_date,
                                          feedback_month, feedback_time, lob, vendor, location, partner,
                                          dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
                                          FROM feedback_data
                                          WHERE data_id = :data_id LIMIT 1";
                    $stmt_feedback_alt = $this->conn->prepare($query_feedback_alt);
                    $stmt_feedback_alt->bindParam(':data_id', $data_id);
                    $stmt_feedback_alt->execute();
                    $feedback_row = $stmt_feedback_alt->fetch(PDO::FETCH_ASSOC);

                    if (!$feedback_row) {
                        error_log("Warning: Still no feedback data found for data_id: $data_id. Using null values for all fields.");
                    } else {
                        error_log("Found feedback data using alternative query for data_id: $data_id");
                    }
                }
            } catch (PDOException $e) {
                error_log("ERROR in feedback query: " . $e->getMessage());
                $feedback_row = null;
            }

            // Extract the additional fields with null defaults
            $domain_category = isset($feedback_row['domain_category']) ? $feedback_row['domain_category'] : null;
            $resolution_comment = isset($feedback_row['resolution_comment']) ? $feedback_row['resolution_comment'] : null;
            $internal_scores = isset($feedback_row['internal_scores']) ? $feedback_row['internal_scores'] : null;
            $feedback_submit_date = isset($feedback_row['feedback_submit_date']) ? $feedback_row['feedback_submit_date'] : null;
            $feedback_month = isset($feedback_row['feedback_month']) ? $feedback_row['feedback_month'] : null;
            $feedback_time = isset($feedback_row['feedback_time']) ? $feedback_row['feedback_time'] : null;
            $lob = isset($feedback_row['lob']) ? $feedback_row['lob'] : null;
            $vendor = isset($feedback_row['vendor']) ? $feedback_row['vendor'] : null;
            $location = isset($feedback_row['location']) ? $feedback_row['location'] : null;
            $partner = isset($feedback_row['partner']) ? $feedback_row['partner'] : null;
            $dummy_1 = isset($feedback_row['dummy_1']) ? $feedback_row['dummy_1'] : null;
            $dummy_2 = isset($feedback_row['dummy_2']) ? $feedback_row['dummy_2'] : null;
            $dummy_3 = isset($feedback_row['dummy_3']) ? $feedback_row['dummy_3'] : null;
            $dummy_4 = isset($feedback_row['dummy_4']) ? $feedback_row['dummy_4'] : null;
            $dummy_5 = isset($feedback_row['dummy_5']) ? $feedback_row['dummy_5'] : null;

            // Insert into comment_queue with all fields
            $query = "INSERT INTO comment_queue (
                comment, data_id, user_id, csat, nps, pid, status,
                domain_category, resolution_comment, internal_scores, feedback_submit_date,
                feedback_month, feedback_time, lob, vendor, location, partner,
                dummy_1, dummy_2, dummy_3, dummy_4, dummy_5
            ) VALUES (
                :comment, :data_id, :user_id, :csat, :nps, :pid, 'pending',
                :domain_category, :resolution_comment, :internal_scores, :feedback_submit_date,
                :feedback_month, :feedback_time, :lob, :vendor, :location, :partner,
                :dummy_1, :dummy_2, :dummy_3, :dummy_4, :dummy_5
            )";

            $stmt = $this->conn->prepare($query);
            if (!$stmt) {
                error_log("ERROR: Failed to prepare insert query: " . json_encode($this->conn->errorInfo()));
                return false;
            }

            $stmt->bindParam(':comment', $comment);
            $stmt->bindParam(':data_id', $data_id);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':csat', $csat);
            $stmt->bindParam(':nps', $nps);
            $stmt->bindParam(':pid', $pid);
            $stmt->bindParam(':domain_category', $domain_category);
            $stmt->bindParam(':resolution_comment', $resolution_comment);
            $stmt->bindParam(':internal_scores', $internal_scores);
            $stmt->bindParam(':feedback_submit_date', $feedback_submit_date);
            $stmt->bindParam(':feedback_month', $feedback_month);
            $stmt->bindParam(':feedback_time', $feedback_time);
            $stmt->bindParam(':lob', $lob);
            $stmt->bindParam(':vendor', $vendor);
            $stmt->bindParam(':location', $location);
            $stmt->bindParam(':partner', $partner);
            $stmt->bindParam(':dummy_1', $dummy_1);
            $stmt->bindParam(':dummy_2', $dummy_2);
            $stmt->bindParam(':dummy_3', $dummy_3);
            $stmt->bindParam(':dummy_4', $dummy_4);
            $stmt->bindParam(':dummy_5', $dummy_5);

            // Execute with detailed error handling
            try {
                $result = $stmt->execute();
                if ($result) {
                    error_log("Successfully enqueued comment for data_id: $data_id");
                    return true;
                } else {
                    $errorInfo = $stmt->errorInfo();
                    error_log("Failed to enqueue comment for data_id: $data_id. Error: " . json_encode($errorInfo));
                    return false;
                }
            } catch (PDOException $e) {
                error_log("PDO Exception in enqueueComment for data_id: $data_id - " . $e->getMessage());
                return false;
            }
        } catch (Exception $e) {
            error_log("Exception in enqueueComment for data_id: $data_id - " . $e->getMessage());
            return false;
        }
    }

    public function dequeueComment($queue_id) {
        $query = "DELETE FROM comment_queue WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $queue_id);
        return $stmt->execute();
    }

    public function getQueuedComments($batch_size) {
        $query = "SELECT * FROM comment_queue WHERE status = 'pending' LIMIT :batch_size";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':batch_size', $batch_size, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

	    public function isCommentInQueue($comment, $data_id, $pid = null) {
        $query = "SELECT COUNT(*) AS count FROM comment_queue WHERE comment = :comment AND data_id = :data_id";

        // Only add pid to the query if it's provided
        if ($pid !== null) {
            $query .= " AND pid = :pid";
        }

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':comment', $comment);
        $stmt->bindParam(':data_id', $data_id);

        // Only bind pid if it's provided
        if ($pid !== null) {
            $stmt->bindParam(':pid', $pid);
        }

        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
    }

    public function getDataIdsByDomainCategory($user_id, $domain_category) {
        if ($domain_category === 'all') {
            return $this->getDataIdsByUserId($user_id);
        }

        $query = "SELECT DISTINCT ac.data_id
                  FROM analyzed_comments ac
                  WHERE ac.user_id = :user_id
                  AND ac.domain_category = :domain_category
                  ORDER BY ac.data_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->bindParam(':domain_category', $domain_category, PDO::PARAM_STR);
        $stmt->execute();

        // Return in the same format as getDataIdsByUserId
        $results = $stmt->fetchAll(PDO::FETCH_COLUMN);
        return array_map(function($id) {
            return ['data_id' => $id];
        }, $results);
    }


    public function getMainDriversForSentiment($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
        $query = "SELECT main_driver, COUNT(*) as count
                  FROM analyzed_comments
                  WHERE user_id = :user_id";

        $params = [':user_id' => $user_id];

        if ($data_id !== null && $data_id !== 'all') {
            $query .= " AND data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($sentiment !== null && $sentiment !== 'all') {
            $query .= " AND sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null && $domain_category !== 'all') {
            $query .= " AND domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        $query .= " GROUP BY main_driver ORDER BY count DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDetailedSubDriverMetrics($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
        $query = "SELECT
                    sub_driver,
                    COUNT(*) as total_count,
                    AVG(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_ratio,
                    AVG(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_ratio,
                    AVG(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_ratio,
                    AVG(csat) as avg_csat,
                    AVG(nps) as avg_nps
                FROM analyzed_comments
                WHERE user_id = :user_id";

        $params = [':user_id' => $user_id];

        if ($data_id !== null && $data_id !== 'all') {
            $query .= " AND data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($sentiment !== null && $sentiment !== 'all') {
            $query .= " AND sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null && $domain_category !== 'all') {
            $query .= " AND domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        $query .= " GROUP BY sub_driver ORDER BY total_count DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getTimelineMetrics($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
        $query = "SELECT
                    DATE(IFNULL(feedback_submit_date, created_at)) as date,
                    COUNT(*) as total,
                    SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_count,
                    SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_count,
                    SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_count,
                    AVG(csat) as avg_csat,
                    AVG(nps) as avg_nps
                FROM analyzed_comments
                WHERE user_id = :user_id";

        $params = [':user_id' => $user_id];

        if ($data_id !== null && $data_id !== 'all') {
            $query .= " AND data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($sentiment !== null && $sentiment !== 'all') {
            $query .= " AND sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null && $domain_category !== 'all') {
            $query .= " AND domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        $query .= " GROUP BY DATE(IFNULL(feedback_submit_date, created_at)) ORDER BY date";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getFilteredData($user_id, $data_id = null, $sentiment = null, $domain_category = null) {
        return $this->getAnalyzedComments($data_id, $user_id, $sentiment, $domain_category);
    }

    public function getSentimentDrivers($data_id = null, $user_id = null, $sentiment = null, $domain_category = null) {
        $query = "SELECT
                    main_driver,
                    sub_driver,
                    sentiment,
                    COUNT(*) as count
                  FROM analyzed_comments
                  WHERE user_id = :user_id";

        $params = [':user_id' => $user_id];

        if ($data_id !== null && $data_id !== 'all') {
            $query .= " AND data_id = :data_id";
            $params[':data_id'] = $data_id;
        }

        if ($sentiment !== null && $sentiment !== 'all') {
            $query .= " AND sentiment = :sentiment";
            $params[':sentiment'] = $sentiment;
        }

        if ($domain_category !== null && $domain_category !== 'all') {
            $query .= " AND domain_category = :domain_category";
            $params[':domain_category'] = $domain_category;
        }

        $query .= " GROUP BY main_driver, sub_driver, sentiment ORDER BY main_driver, sub_driver, sentiment";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);

        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $drivers_data = [];

        foreach ($results as $row) {
            if (!isset($drivers_data[$row['main_driver']])) {
                $drivers_data[$row['main_driver']] = [];
            }

            if (!isset($drivers_data[$row['main_driver']][$row['sub_driver']])) {
                $drivers_data[$row['main_driver']][$row['sub_driver']] = [
                    'Positive' => 0,
                    'Neutral' => 0,
                    'Negative' => 0
                ];
            }

            $drivers_data[$row['main_driver']][$row['sub_driver']][$row['sentiment']] = (int)$row['count'];
        }

        return $drivers_data;
    }

    // Method to get sentiment distribution by LOB
        public function getLobSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                   $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                   $team = null, $resolution_status = null) {
        error_log("getLobSentiment called with data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");

        $sql = "SELECT lob, sentiment, COUNT(*) as count  FROM analyzed_comments WHERE user_id = ? AND lob IS NOT NULL AND lob != ''";
        $params = [$user_id];

        // Handle data_id filter
        if ($data_id !== 'all' && $data_id !== null) {
            $sql .= " AND data_id = ?";
            $params[] = $data_id;
            error_log("Adding data_id filter: $data_id");
        } else {
            error_log("No data_id filter applied (all)");
        }

        // Handle sentiment filter
        if ($sentiment !== 'all' && $sentiment !== null) {
            $sql .= " AND sentiment = ?";
            $params[] = $sentiment;
            error_log("Adding sentiment filter: $sentiment");
        } else {
            error_log("No sentiment filter applied (all)");
        }

        // Handle domain_category filter
        if ($domain_category !== 'all' && $domain_category !== null) {
            // Handle domain_category properly by using LIKE for hierarchical matching
            $sql .= " AND (domain_category = ? OR domain_category LIKE ? OR domain_category LIKE ? OR domain_category LIKE ?)";
            $params[] = $domain_category;
            $params[] = $domain_category . '/%';  // For child categories
            $params[] = '%/' . $domain_category;  // For parent categories
            $params[] = '%/' . $domain_category . '/%';  // For middle categories
            error_log("Adding domain_category filter: $domain_category");
        } else {
            error_log("No domain_category filter applied (all)");
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= ?";
            $params[] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= ?";
            $params[] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $sql .= " AND partner = ?";
            $params[] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $sql .= " AND lob = ?";
            $params[] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $sql .= " AND dummy_1 = ?";
            $params[] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $sql .= " AND dummy_5 = ?";
            $params[] = $resolution_status;
        }

        $sql .= " GROUP BY lob, sentiment";

        try {
            error_log("Executing SQL: $sql with params: " . json_encode($params));
            $stmt = $this->conn->prepare($sql);

            if (!$stmt) {
                error_log("Error preparing statement");
                return [];
            }

            // Bind parameters using PDO style
            foreach ($params as $i => $param) {
                $stmt->bindValue($i + 1, $param);
            }

            $stmt->execute();

            $lob_data = [];

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (!isset($lob_data[$row['lob']])) {
                    $lob_data[$row['lob']] = [
                        'Positive' => 0,
                        'Neutral' => 0,
                        'Negative' => 0
                    ];
                }

                $lob_data[$row['lob']][$row['sentiment']] = (int)$row['count'];
            }
            return $lob_data;
        } catch (Exception $e) {
            error_log("Error in getLobSentiment: " . $e->getMessage());
            return []; // Return empty array on error
        }
    }

    // Method to get sentiment distribution by Vendor
    public function getVendorSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                      $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                      $team = null, $resolution_status = null) {
        error_log("getVendorSentiment called with data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");

        $sql = "SELECT vendor, sentiment, COUNT(*) as count FROM analyzed_comments WHERE user_id = ? AND vendor IS NOT NULL AND vendor != ''";
        $params = [$user_id];

        // Handle data_id filter
        if ($data_id !== 'all' && $data_id !== null) {
            $sql .= " AND data_id = ?";
            $params[] = $data_id;
            error_log("Adding data_id filter: $data_id");
        } else {
            error_log("No data_id filter applied (all)");
        }

        // Handle sentiment filter
        if ($sentiment !== 'all' && $sentiment !== null) {
            $sql .= " AND sentiment = ?";
            $params[] = $sentiment;
            error_log("Adding sentiment filter: $sentiment");
        } else {
            error_log("No sentiment filter applied (all)");
        }

        // Handle domain_category filter
        if ($domain_category !== 'all' && $domain_category !== null) {
            // Handle domain_category properly by using LIKE for hierarchical matching
            $sql .= " AND (domain_category = ? OR domain_category LIKE ? OR domain_category LIKE ? OR domain_category LIKE ?)";
            $params[] = $domain_category;
            $params[] = $domain_category . '/%';  // For child categories
            $params[] = '%/' . $domain_category;  // For parent categories
            $params[] = '%/' . $domain_category . '/%';  // For middle categories
            error_log("Adding domain_category filter: $domain_category");
        } else {
            error_log("No domain_category filter applied (all)");
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= ?";
            $params[] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= ?";
            $params[] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $sql .= " AND partner = ?";
            $params[] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $sql .= " AND lob = ?";
            $params[] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $sql .= " AND dummy_1 = ?";
            $params[] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $sql .= " AND dummy_5 = ?";
            $params[] = $resolution_status;
        }

        $sql .= " GROUP BY vendor, sentiment";

        try {
            error_log("Executing SQL: $sql with params: " . json_encode($params));
            $stmt = $this->conn->prepare($sql);

            if (!$stmt) {
                error_log("Error preparing statement");
                return [];
            }

            // Bind parameters using PDO style
            foreach ($params as $i => $param) {
                $stmt->bindValue($i + 1, $param);
            }

            $stmt->execute();

            $vendor_data = [];

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (!isset($vendor_data[$row['vendor']])) {
                    $vendor_data[$row['vendor']] = [
                        'Positive' => 0,
                        'Neutral' => 0,
                        'Negative' => 0
                    ];
                }

                $vendor_data[$row['vendor']][$row['sentiment']] = (int)$row['count'];
            }
            return $vendor_data;
        } catch (Exception $e) {
            error_log("Error in getVendorSentiment: " . $e->getMessage());
            return []; // Return empty array on error
        }

    }

    // Method to get sentiment distribution by Location
    public function getLocationSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                        $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                        $team = null, $resolution_status = null) {
        error_log("getLocationSentiment called with data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");

        $sql = "SELECT location, sentiment, COUNT(*) as count FROM analyzed_comments WHERE user_id = ? AND location IS NOT NULL AND location != ''";
        $params = [$user_id];

        // Handle data_id filter
        if ($data_id !== 'all' && $data_id !== null) {
            $sql .= " AND data_id = ?";
            $params[] = $data_id;
            error_log("Adding data_id filter: $data_id");
        } else {
            error_log("No data_id filter applied (all)");
        }

        // Handle sentiment filter
        if ($sentiment !== 'all' && $sentiment !== null) {
            $sql .= " AND sentiment = ?";
            $params[] = $sentiment;
            error_log("Adding sentiment filter: $sentiment");
        } else {
            error_log("No sentiment filter applied (all)");
        }

        // Handle domain_category filter
        if ($domain_category !== 'all' && $domain_category !== null) {
            // Handle domain_category properly by using LIKE for hierarchical matching
            $sql .= " AND (domain_category = ? OR domain_category LIKE ? OR domain_category LIKE ? OR domain_category LIKE ?)";
            $params[] = $domain_category;
            $params[] = $domain_category . '/%';  // For child categories
            $params[] = '%/' . $domain_category;  // For parent categories
            $params[] = '%/' . $domain_category . '/%';  // For middle categories
            error_log("Adding domain_category filter: $domain_category");
        } else {
            error_log("No domain_category filter applied (all)");
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= ?";
            $params[] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= ?";
            $params[] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $sql .= " AND partner = ?";
            $params[] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $sql .= " AND lob = ?";
            $params[] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $sql .= " AND dummy_1 = ?";
            $params[] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $sql .= " AND dummy_5 = ?";
            $params[] = $resolution_status;
        }

        $sql .= " GROUP BY location, sentiment";

        try {
            error_log("Executing SQL: $sql with params: " . json_encode($params));
            $stmt = $this->conn->prepare($sql);

            if (!$stmt) {
                error_log("Error preparing statement");
                return [];
            }

            // Bind parameters using PDO style
            foreach ($params as $i => $param) {
                $stmt->bindValue($i + 1, $param);
            }

            $stmt->execute();

            $location_data = [];

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (!isset($location_data[$row['location']])) {
                    $location_data[$row['location']] = [
                        'Positive' => 0,
                        'Neutral' => 0,
                        'Negative' => 0
                    ];
                }

                $location_data[$row['location']][$row['sentiment']] = (int)$row['count'];
            }
            return $location_data;
        } catch (Exception $e) {
            error_log("Error in getLocationSentiment: " . $e->getMessage());
            return []; // Return empty array on error
        }
    }

    // Method to get sentiment distribution by Partner
    public function getPartnerSentiment($data_id, $user_id, $sentiment = null, $domain_category = null,
                                       $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                       $team = null, $resolution_status = null) {
        error_log("getPartnerSentiment called with data_id=$data_id, user_id=$user_id, sentiment=$sentiment, domain_category=$domain_category");

        $sql = "SELECT partner, sentiment, COUNT(*) as count FROM analyzed_comments WHERE user_id = ? AND partner IS NOT NULL AND partner != ''";
        $params = [$user_id];

        // Handle data_id filter
        if ($data_id !== 'all' && $data_id !== null) {
            $sql .= " AND data_id = ?";
            $params[] = $data_id;
            error_log("Adding data_id filter: $data_id");
        } else {
            error_log("No data_id filter applied (all)");
        }

        // Handle sentiment filter
        if ($sentiment !== 'all' && $sentiment !== null) {
            $sql .= " AND sentiment = ?";
            $params[] = $sentiment;
            error_log("Adding sentiment filter: $sentiment");
        } else {
            error_log("No sentiment filter applied (all)");
        }

        // Handle domain_category filter
        if ($domain_category !== 'all' && $domain_category !== null) {
            // Handle domain_category properly by using LIKE for hierarchical matching
            $sql .= " AND (domain_category = ? OR domain_category LIKE ? OR domain_category LIKE ? OR domain_category LIKE ?)";
            $params[] = $domain_category;
            $params[] = $domain_category . '/%';  // For child categories
            $params[] = '%/' . $domain_category;  // For parent categories
            $params[] = '%/' . $domain_category . '/%';  // For middle categories
            error_log("Adding domain_category filter: $domain_category");
        } else {
            error_log("No domain_category filter applied (all)");
        }

        // Add new filter parameters
        if ($start_date !== null && $start_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= ?";
            $params[] = $start_date;
        }

        if ($end_date !== null && $end_date !== '') {
            $sql .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= ?";
            $params[] = $end_date;
        }

        if ($product_type !== null && $product_type !== 'all') {
            $sql .= " AND partner = ?";
            $params[] = $product_type;
        }

        if ($channel_type !== null && $channel_type !== 'all') {
            $sql .= " AND lob = ?";
            $params[] = $channel_type;
        }

        if ($team !== null && $team !== 'all') {
            $sql .= " AND dummy_1 = ?";
            $params[] = $team;
        }

        if ($resolution_status !== null && $resolution_status !== 'all') {
            $sql .= " AND dummy_5 = ?";
            $params[] = $resolution_status;
        }

        $sql .= " GROUP BY partner, sentiment";

        try {
            error_log("Executing SQL: $sql with params: " . json_encode($params));
            $stmt = $this->conn->prepare($sql);

            if (!$stmt) {
                error_log("Error preparing statement");
                return [];
            }

            // Bind parameters using PDO style
            foreach ($params as $i => $param) {
                $stmt->bindValue($i + 1, $param);
            }

            $stmt->execute();

            $partner_data = [];

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                if (!isset($partner_data[$row['partner']])) {
                    $partner_data[$row['partner']] = [
                        'Positive' => 0,
                        'Neutral' => 0,
                        'Negative' => 0
                    ];
                }

                $partner_data[$row['partner']][$row['sentiment']] = (int)$row['count'];
            }
            return $partner_data;
        } catch (Exception $e) {
            error_log("Error in getPartnerSentiment: " . $e->getMessage());
            return []; // Return empty array on error
        }
    }

    /**
     * Get consolidated summary from feedback_summaries table
     * When all filters are default (All), it consolidates all summaries
     * Otherwise, it returns the specific summary for the selected data_id
     */
    public function getConsolidatedSummary($data_id = null, $user_id = null, $domain_category = null,
                                          $start_date = null, $end_date = null, $product_type = null, $channel_type = null,
                                          $team = null, $resolution_status = null) {
        try {
            // Ensure user_id is provided
            if ($user_id === null) {
                return [
                    'summary' => "Error: User ID is required.",
                    'is_consolidated' => false
                ];
            }

            // Get the total comments count directly from analyzed_comments table
            $totalCommentsQuery = "SELECT COUNT(*) as total FROM analyzed_comments WHERE user_id = :user_id";
            $totalCommentsParams = [':user_id' => $user_id];

            if ($data_id !== null && $data_id !== 'all') {
                $totalCommentsQuery .= " AND data_id = :data_id";
                $totalCommentsParams[':data_id'] = $data_id;
            }

            if ($domain_category !== null && $domain_category !== 'all') {
                $totalCommentsQuery .= " AND domain_category = :domain_category";
                $totalCommentsParams[':domain_category'] = $domain_category;
            }

            $totalCommentsStmt = $this->conn->prepare($totalCommentsQuery);
            $totalCommentsStmt->execute($totalCommentsParams);
            $totalCommentsResult = $totalCommentsStmt->fetch(PDO::FETCH_ASSOC);
            $actualTotalComments = $totalCommentsResult['total'];

            // Get sentiment counts directly from analyzed_comments table
            $sentimentCountsQuery = "SELECT
                SUM(CASE WHEN sentiment = 'Positive' THEN 1 ELSE 0 END) as positive_count,
                SUM(CASE WHEN sentiment = 'Negative' THEN 1 ELSE 0 END) as negative_count,
                SUM(CASE WHEN sentiment = 'Neutral' THEN 1 ELSE 0 END) as neutral_count
                FROM analyzed_comments
                WHERE user_id = :user_id";

            $sentimentCountsParams = [':user_id' => $user_id];

            if ($data_id !== null && $data_id !== 'all') {
                $sentimentCountsQuery .= " AND data_id = :data_id";
                $sentimentCountsParams[':data_id'] = $data_id;
            }

            if ($domain_category !== null && $domain_category !== 'all') {
                $sentimentCountsQuery .= " AND domain_category = :domain_category";
                $sentimentCountsParams[':domain_category'] = $domain_category;
            }

            $sentimentCountsStmt = $this->conn->prepare($sentimentCountsQuery);
            $sentimentCountsStmt->execute($sentimentCountsParams);
            $sentimentCounts = $sentimentCountsStmt->fetch(PDO::FETCH_ASSOC);

            $actualPositiveCount = $sentimentCounts['positive_count'] ?: 0;
            $actualNegativeCount = $sentimentCounts['negative_count'] ?: 0;
            $actualNeutralCount = $sentimentCounts['neutral_count'] ?: 0;

            // If specific data_id is selected, return that summary
            if ($data_id !== null && $data_id !== 'all') {
                $query = "SELECT summary FROM feedback_summaries
                          WHERE data_id = :data_id AND user_id = :user_id";
                $params = [
                    ':data_id' => $data_id,
                    ':user_id' => $user_id
                ];

                if ($domain_category !== null && $domain_category !== 'all') {
                    $query .= " AND domain_category = :domain_category";
                    $params[':domain_category'] = $domain_category;
                }

                $stmt = $this->conn->prepare($query);
                $stmt->execute($params);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($result && isset($result['summary'])) {
                    return [
                        'summary' => $result['summary'],
                        'is_consolidated' => false
                    ];
                }
            }

            // If no specific data_id or no summary found, consolidate all summaries
            $query = "SELECT fs.summary, fs.domain_category, fs.data_id
                      FROM feedback_summaries fs
                      WHERE fs.user_id = :user_id";

            $params = [':user_id' => $user_id];

            if ($domain_category !== null && $domain_category !== 'all') {
                $query .= " AND fs.domain_category = :domain_category";
                $params[':domain_category'] = $domain_category;
            }

            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            $summaries = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($summaries)) {
                return [
                    'summary' => "No summaries available for the selected filters.",
                    'is_consolidated' => true
                ];
            }

            // Extract key insights from all summaries
            $keyInsights = [];
            $categories = [];
            $mainDrivers = [];
            $subDrivers = [];

            // Get unique domain categories
            $domainCategoriesQuery = "SELECT DISTINCT domain_category FROM analyzed_comments
                                     WHERE user_id = :user_id AND domain_category IS NOT NULL AND domain_category != ''";
            $domainCategoriesParams = [':user_id' => $user_id];

            if ($data_id !== null && $data_id !== 'all') {
                $domainCategoriesQuery .= " AND data_id = :data_id";
                $domainCategoriesParams[':data_id'] = $data_id;
            }

            if ($domain_category !== null && $domain_category !== 'all') {
                $domainCategoriesQuery .= " AND domain_category = :domain_category";
                $domainCategoriesParams[':domain_category'] = $domain_category;
            }

            $domainCategoriesStmt = $this->conn->prepare($domainCategoriesQuery);
            $domainCategoriesStmt->execute($domainCategoriesParams);
            $categories = $domainCategoriesStmt->fetchAll(PDO::FETCH_COLUMN);

            foreach ($summaries as $summary) {
                // Extract key points from the summary
                $summaryText = $summary['summary'];

                // Extract main drivers and sub-drivers
                if (preg_match_all('/Primary Issue \(L1\):\s*(.*?)(?=\n|$)/i', $summaryText, $matches)) {
                    foreach ($matches[1] as $driver) {
                        $driver = trim($driver);
                        if (!empty($driver) && !in_array($driver, $mainDrivers)) {
                            $mainDrivers[] = $driver;
                        }
                    }
                }

                if (preg_match_all('/Secondary Issue \(L2\):\s*(.*?)(?=\n|$)/i', $summaryText, $matches)) {
                    foreach ($matches[1] as $driver) {
                        $driver = trim($driver);
                        if (!empty($driver) && !in_array($driver, $subDrivers)) {
                            $subDrivers[] = $driver;
                        }
                    }
                }

                // Extract key insights
                if (preg_match_all('/(?:Key Insight|Key Finding|Important Point)(?:[s:]|\s*\d+:)?\s*(.*?)(?=\n\n|\n[A-Z]|$)/is', $summaryText, $matches)) {
                    foreach ($matches[1] as $insight) {
                        $insight = trim($insight);
                        if (!empty($insight)) {
                            $keyInsights[] = $insight;
                        }
                    }
                }
            }

            // Calculate percentages
            $positivePercentage = $actualTotalComments > 0 ? round(($actualPositiveCount / $actualTotalComments) * 100) : 0;
            $negativePercentage = $actualTotalComments > 0 ? round(($actualNegativeCount / $actualTotalComments) * 100) : 0;
            $neutralPercentage = $actualTotalComments > 0 ? round(($actualNeutralCount / $actualTotalComments) * 100) : 0;

            // Limit to top insights
            $keyInsights = array_slice($keyInsights, 0, 5);
            $mainDrivers = array_slice($mainDrivers, 0, 5);
            $subDrivers = array_slice($subDrivers, 0, 5);

            // Get count of unique data_ids
            $datasetCountQuery = "SELECT COUNT(DISTINCT data_id) as count FROM analyzed_comments
                                 WHERE user_id = :user_id";
            $datasetCountParams = [':user_id' => $user_id];

            if ($data_id !== null && $data_id !== 'all') {
                $datasetCountQuery .= " AND data_id = :data_id";
                $datasetCountParams[':data_id'] = $data_id;
            }

            if ($domain_category !== null && $domain_category !== 'all') {
                $datasetCountQuery .= " AND domain_category = :domain_category";
                $datasetCountParams[':domain_category'] = $domain_category;
            }

            $datasetCountStmt = $this->conn->prepare($datasetCountQuery);
            $datasetCountStmt->execute($datasetCountParams);
            $datasetCountResult = $datasetCountStmt->fetch(PDO::FETCH_ASSOC);
            $datasetCount = $datasetCountResult['count'];

            // Create consolidated summary
            $consolidatedSummary = "Based on the analysis of {$actualTotalComments} customer feedback comments across " . $datasetCount . " datasets";

            if (!empty($categories)) {
                $consolidatedSummary .= " in the " . implode(", ", $categories) . " domain" . (count($categories) > 1 ? "s" : "");
            }

            $consolidatedSummary .= ", the overall sentiment is {$positivePercentage}% positive, {$neutralPercentage}% neutral, and {$negativePercentage}% negative.\n\n";

            if (!empty($mainDrivers)) {
                $consolidatedSummary .= "The primary issues identified are: " . implode(", ", $mainDrivers) . ".\n\n";
            }

            if (!empty($subDrivers)) {
                $consolidatedSummary .= "Key secondary issues include: " . implode(", ", $subDrivers) . ".\n\n";
            }

            if (!empty($keyInsights)) {
                $consolidatedSummary .= "Key insights:\n";
                foreach ($keyInsights as $index => $insight) {
                    $consolidatedSummary .= "• " . $insight . "\n";
                }
            }

            return [
                'summary' => $consolidatedSummary,
                'is_consolidated' => true,
                'stats' => [
                    'total_comments' => $actualTotalComments,
                    'positive_percentage' => $positivePercentage,
                    'negative_percentage' => $negativePercentage,
                    'neutral_percentage' => $neutralPercentage,
                    'datasets_count' => $datasetCount
                ]
            ];

        } catch (PDOException $e) {
            error_log("Error in getConsolidatedSummary: " . $e->getMessage());
            return [
                'summary' => "Error retrieving summaries: " . $e->getMessage(),
                'is_consolidated' => true
            ];
        }
    }

    /**
     * Get scale configuration for a specific data upload
     * Returns scale configuration or legacy defaults for backward compatibility
     */
    public function getScaleConfiguration($data_id, $user_id) {
        $query = "SELECT * FROM upload_scale_configurations
                  WHERE data_id = :data_id AND user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([':data_id' => $data_id, ':user_id' => $user_id]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$config) {
            // Return legacy defaults for backward compatibility
            return $this->getLegacyScaleDefaults();
        }

        return $config;
    }

    /**
     * Get legacy scale defaults for backward compatibility
     */
    private function getLegacyScaleDefaults() {
        return [
            'csat_scale_type' => '1-10',
            'csat_min_value' => 1,
            'csat_max_value' => 10,
            'csat_baseline' => 8.0,
            'nps_scale_type' => '0-10',
            'nps_min_value' => 0,
            'nps_max_value' => 10,
            'nps_promoter_threshold' => 9,
            'nps_detractor_threshold' => 6,
            'internal_score_scale_type' => 'not_used',
            'internal_score_min_value' => null,
            'internal_score_max_value' => null,
            'internal_score_baseline' => null
        ];
    }

    // New methods for enhanced filter options
    public function getDistinctPartners($user_id) {
        $sql = "SELECT DISTINCT partner FROM analyzed_comments WHERE user_id = ? AND partner IS NOT NULL AND partner != '' ORDER BY partner";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDistinctLobs($user_id) {
        $sql = "SELECT DISTINCT lob FROM analyzed_comments WHERE user_id = ? AND lob IS NOT NULL AND lob != '' ORDER BY lob";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDistinctTeams($user_id) {
        $sql = "SELECT DISTINCT dummy_1 as team FROM analyzed_comments WHERE user_id = ? AND dummy_1 IS NOT NULL AND dummy_1 != '' ORDER BY dummy_1";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDistinctResolutionStatuses($user_id) {
        $sql = "SELECT DISTINCT partner FROM analyzed_comments WHERE user_id = ? AND partner IS NOT NULL AND partner != '' ORDER BY partner";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getDateRange($user_id) {
        $sql = "SELECT
                    MIN(DATE(IFNULL(feedback_submit_date, created_at))) as min_date,
                    MAX(DATE(IFNULL(feedback_submit_date, created_at))) as max_date
                FROM analyzed_comments
                WHERE user_id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$user_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Validate if a filter combination has any data
     */
    public function validateFilterCombination($user_id, $filters = []) {
        $query = "SELECT COUNT(*) as count FROM analyzed_comments WHERE user_id = :user_id";
        $params = [':user_id' => $user_id];

        // Add filter conditions
        if (!empty($filters['data_id']) && $filters['data_id'] !== 'all') {
            $query .= " AND data_id = :data_id";
            $params[':data_id'] = $filters['data_id'];
        }

        if (!empty($filters['sentiment']) && $filters['sentiment'] !== 'all') {
            $query .= " AND sentiment = :sentiment";
            $params[':sentiment'] = $filters['sentiment'];
        }

        if (!empty($filters['domain_category']) && $filters['domain_category'] !== 'all') {
            $query .= " AND domain_category = :domain_category";
            $params[':domain_category'] = $filters['domain_category'];
        }

        if (!empty($filters['start_date'])) {
            $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) >= :start_date";
            $params[':start_date'] = $filters['start_date'];
        }

        if (!empty($filters['end_date'])) {
            $query .= " AND DATE(IFNULL(feedback_submit_date, created_at)) <= :end_date";
            $params[':end_date'] = $filters['end_date'];
        }

        if (!empty($filters['partner']) && $filters['partner'] !== 'all') {
            $query .= " AND partner = :partner";
            $params[':partner'] = $filters['partner'];
        }

        if (!empty($filters['lob']) && $filters['lob'] !== 'all') {
            $query .= " AND lob = :lob";
            $params[':lob'] = $filters['lob'];
        }

        if (!empty($filters['dummy_1']) && $filters['dummy_1'] !== 'all') {
            $query .= " AND dummy_1 = :dummy_1";
            $params[':dummy_1'] = $filters['dummy_1'];
        }

        if (!empty($filters['dummy_5']) && $filters['dummy_5'] !== 'all') {
            $query .= " AND dummy_5 = :dummy_5";
            $params[':dummy_5'] = $filters['dummy_5'];
        }

        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['count'] > 0;
    }



}
