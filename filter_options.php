<?php
/**
 * Dynamic Filter Options API
 * Provides real-time filter options based on current filter state
 */

session_start();
require_once __DIR__ . '/config.php';
require_once 'filter_validation.php';

// Check if user is logged in
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Session expired or invalid']);
    exit();
}

header('Content-Type: application/json');

$user_id = $_SESSION['user_id'];
$filter_validator = new FilterValidation();

try {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_options':
            handleGetOptions($filter_validator, $user_id);
            break;
            
        case 'validate_combination':
            handleValidateCombination($filter_validator, $user_id);
            break;
            
        case 'batch_options':
            handleBatchOptions($filter_validator, $user_id);
            break;
            
        case 'update_filter':
            handleUpdateFilter($filter_validator, $user_id);
            break;

        case 'clear_cache':
            handleClearCache($filter_validator);
            break;

        case 'clean_logs':
            handleCleanLogs($filter_validator);
            break;

        default:
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Filter options API error: " . $e->getMessage());
    echo json_encode(['error' => 'An unexpected error occurred']);
}

/**
 * Handle getting options for a specific filter
 */
function handleGetOptions($filter_validator, $user_id) {
    $filter_type = $_GET['filter_type'] ?? '';
    $current_filters = parseCurrentFilters();
    
    if (empty($filter_type)) {
        echo json_encode(['error' => 'Filter type is required']);
        return;
    }
    
    $options = $filter_validator->getAvailableFilterOptions($user_id, $filter_type, $current_filters);
    
    echo json_encode([
        'success' => true,
        'filter_type' => $filter_type,
        'options' => $options,
        'count' => count($options)
    ]);
}

/**
 * Handle validating a filter combination
 */
function handleValidateCombination($filter_validator, $user_id) {
    $filters = parseCurrentFilters();
    
    $has_data = $filter_validator->validateFilterCombination($user_id, $filters);
    
    echo json_encode([
        'success' => true,
        'has_data' => $has_data,
        'filters' => $filters
    ]);
}

/**
 * Handle batch options request for multiple filters
 */
function handleBatchOptions($filter_validator, $user_id) {
    $filter_types = $_GET['filter_types'] ?? '';
    $current_filters = parseCurrentFilters();
    
    if (empty($filter_types)) {
        echo json_encode(['error' => 'Filter types are required']);
        return;
    }
    
    // Parse filter types (comma-separated)
    $filter_types_array = explode(',', $filter_types);
    $filter_types_array = array_map('trim', $filter_types_array);
    
    $batch_options = $filter_validator->getBatchFilterOptions($user_id, $filter_types_array, $current_filters);
    
    echo json_encode([
        'success' => true,
        'options' => $batch_options,
        'current_filters' => $current_filters
    ]);
}

/**
 * Handle filter update and return updated options for all other filters
 */
function handleUpdateFilter($filter_validator, $user_id) {
    $updated_filter_type = $_GET['updated_filter'] ?? '';
    $updated_filter_value = $_GET['updated_value'] ?? '';
    
    if (empty($updated_filter_type)) {
        echo json_encode(['error' => 'Updated filter type is required']);
        return;
    }
    
    // Get current filters and update the changed one
    $current_filters = parseCurrentFilters();
    $current_filters[$updated_filter_type] = $updated_filter_value;
    
    // Get all filter types except the one that was just updated
    $all_filter_types = [
        'domain_category', 'data_id', 'sentiment', 
        'product_type', 'channel_type', 'team', 'resolution_status'
    ];
    
    $other_filter_types = array_filter($all_filter_types, function($type) use ($updated_filter_type) {
        return $type !== $updated_filter_type;
    });
    
    // Get updated options for all other filters
    $updated_options = $filter_validator->getBatchFilterOptions($user_id, $other_filter_types, $current_filters);
    
    // Validate the new combination
    $has_data = $filter_validator->validateFilterCombination($user_id, $current_filters);
    
    echo json_encode([
        'success' => true,
        'updated_filter' => $updated_filter_type,
        'updated_value' => $updated_filter_value,
        'updated_options' => $updated_options,
        'has_data' => $has_data,
        'current_filters' => $current_filters
    ]);
}

/**
 * Parse current filters from GET parameters
 */
function parseCurrentFilters() {
    $filters = [];
    
    // Standard filters
    if (isset($_GET['data_id']) && $_GET['data_id'] !== 'all' && $_GET['data_id'] !== '') {
        $filters['data_id'] = htmlspecialchars($_GET['data_id']);
    }
    
    if (isset($_GET['sentiment']) && $_GET['sentiment'] !== 'all' && $_GET['sentiment'] !== '') {
        $filters['sentiment'] = htmlspecialchars($_GET['sentiment']);
    }
    
    if (isset($_GET['domain_category']) && $_GET['domain_category'] !== 'all' && $_GET['domain_category'] !== '') {
        $filters['domain_category'] = urldecode(htmlspecialchars_decode($_GET['domain_category'], ENT_QUOTES));
    }
    
    // Date filters
    if (isset($_GET['start_date']) && $_GET['start_date'] !== '') {
        $filters['start_date'] = htmlspecialchars($_GET['start_date']);
    }
    
    if (isset($_GET['end_date']) && $_GET['end_date'] !== '') {
        $filters['end_date'] = htmlspecialchars($_GET['end_date']);
    }
    
    // Extended filters (mapping to database fields)
    if (isset($_GET['product_type']) && $_GET['product_type'] !== 'all' && $_GET['product_type'] !== '') {
        $filters['partner'] = htmlspecialchars($_GET['product_type']);
    }

    if (isset($_GET['channel_type']) && $_GET['channel_type'] !== 'all' && $_GET['channel_type'] !== '') {
        $filters['lob'] = htmlspecialchars($_GET['channel_type']);
    }

    if (isset($_GET['team']) && $_GET['team'] !== 'all' && $_GET['team'] !== '') {
        $filters['dummy_1'] = htmlspecialchars($_GET['team']);
    }

    if (isset($_GET['resolution_status']) && $_GET['resolution_status'] !== 'all' && $_GET['resolution_status'] !== '') {
        $filters['dummy_5'] = htmlspecialchars($_GET['resolution_status']);
    }
    
    return $filters;
}

/**
 * Format options for frontend consumption
 */
function formatOptionsForFrontend($options, $filter_type) {
    $formatted = [];
    
    foreach ($options as $option) {
        $formatted[] = [
            'value' => $option,
            'label' => $option,
            'disabled' => false
        ];
    }
    
    // Add "All" option at the beginning for most filters
    if (!in_array($filter_type, ['start_date', 'end_date'])) {
        array_unshift($formatted, [
            'value' => 'all',
            'label' => 'All ' . ucwords(str_replace('_', ' ', $filter_type)),
            'disabled' => false
        ]);
    }
    
    return $formatted;
}

/**
 * Get filter display name
 */
function getFilterDisplayName($filter_type) {
    $display_names = [
        'domain_category' => 'Domain Categories',
        'data_id' => 'Data IDs',
        'sentiment' => 'Sentiments',
        'product_type' => 'Product Types',
        'channel_type' => 'Channel Types',
        'team' => 'Teams',
        'resolution_status' => 'Resolution Statuses',
        'start_date' => 'Start Date',
        'end_date' => 'End Date'
    ];
    
    return $display_names[$filter_type] ?? ucwords(str_replace('_', ' ', $filter_type));
}

/**
 * Handle cache clearing
 */
function handleClearCache($filter_validator) {
    try {
        $filter_validator->clearCache();
        echo json_encode([
            'success' => true,
            'message' => 'Cache cleared successfully'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to clear cache: ' . $e->getMessage()
        ]);
    }
}

/**
 * Handle log cleaning
 */
function handleCleanLogs($filter_validator) {
    try {
        require_once 'filter_logger.php';
        $logger = new FilterLogger();
        $logger->cleanOldLogs(30); // Keep last 30 days

        echo json_encode([
            'success' => true,
            'message' => 'Old logs cleaned successfully'
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => 'Failed to clean logs: ' . $e->getMessage()
        ]);
    }
}

/**
 * Log API request for debugging
 */
function logApiRequest($action, $parameters, $response_size) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => $action,
        'parameters' => $parameters,
        'response_size' => $response_size,
        'user_id' => $_SESSION['user_id'] ?? 'unknown',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ];
    
    $log_file = __DIR__ . '/logs/filter_api.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}
?>
